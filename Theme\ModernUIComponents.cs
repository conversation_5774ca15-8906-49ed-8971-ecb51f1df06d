using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace ModernWinApp.Theme
{
    public static class ModernUIComponents
    {
        // Modern Card Component
        public static Panel CreateModernCard(string title = "", Size? size = null, Point? location = null)
        {
            var card = new Panel
            {
                Size = size ?? new Size(300, 200),
                Location = location ?? new Point(0, 0),
                BackColor = ThemeManager.Colors.Card,
                BorderStyle = BorderStyle.None,
                Padding = new Padding(ResponsiveManager.Dimensions.Padding),
                RightToLeft = RightToLeft.Yes
            };

            // Add shadow effect
            card.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, card.Width, card.Height);
                using (var brush = new SolidBrush(ThemeManager.Colors.CardShadow))
                {
                    e.Graphics.FillRectangle(brush, rect.X + 2, rect.Y + 2, rect.Width, rect.Height);
                }
                using (var brush = new SolidBrush(card.BackColor))
                {
                    e.Graphics.FillRectangle(brush, rect);
                }
                using (var pen = new Pen(ThemeManager.Colors.Border))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
            };

            if (!string.IsNullOrEmpty(title))
            {
                var titleLabel = new Label
                {
                    Text = title,
                    Font = ResponsiveManager.Fonts.Subtitle,
                    ForeColor = ThemeManager.Colors.Text,
                    Dock = DockStyle.Top,
                    Height = ResponsiveManager.Dimensions.ButtonHeight,
                    TextAlign = ContentAlignment.MiddleRight,
                    RightToLeft = RightToLeft.Yes
                };
                card.Controls.Add(titleLabel);
            }

            return card;
        }

        // Modern Button with Gradient
        public static Guna2Button CreateModernButton(string text, ModernButtonStyle style = ModernButtonStyle.Primary)
        {
            var button = new Guna2Button
            {
                Text = text,
                Font = ResponsiveManager.Fonts.Regular,
                BorderRadius = ResponsiveManager.Dimensions.BorderRadius,
                Size = new Size(150, ResponsiveManager.Dimensions.ButtonHeight),
                Cursor = Cursors.Hand,
                Animated = true,
                RightToLeft = RightToLeft.Yes,
                TextAlign = HorizontalAlignment.Center
            };

            ApplyButtonStyle(button, style);
            return button;
        }

        public enum ModernButtonStyle
        {
            Primary,
            Secondary,
            Success,
            Warning,
            Error,
            Info,
            Gradient
        }

        private static void ApplyButtonStyle(Guna2Button button, ModernButtonStyle style)
        {
            switch (style)
            {
                case ModernButtonStyle.Primary:
                    button.FillColor = ThemeManager.Colors.Accent;
                    button.HoverState.FillColor = ThemeManager.Colors.AccentLight;
                    button.ForeColor = Color.White;
                    break;

                case ModernButtonStyle.Secondary:
                    button.FillColor = ThemeManager.Colors.Surface;
                    button.HoverState.FillColor = ThemeManager.Colors.Border;
                    button.ForeColor = ThemeManager.Colors.Text;
                    break;

                case ModernButtonStyle.Success:
                    button.FillColor = ThemeManager.Colors.Success;
                    button.HoverState.FillColor = Color.FromArgb(34, 197, 94);
                    button.ForeColor = Color.White;
                    break;

                case ModernButtonStyle.Warning:
                    button.FillColor = ThemeManager.Colors.Warning;
                    button.HoverState.FillColor = Color.FromArgb(251, 191, 36);
                    button.ForeColor = Color.White;
                    break;

                case ModernButtonStyle.Error:
                    button.FillColor = ThemeManager.Colors.Error;
                    button.HoverState.FillColor = Color.FromArgb(248, 113, 113);
                    button.ForeColor = Color.White;
                    break;

                case ModernButtonStyle.Info:
                    button.FillColor = ThemeManager.Colors.Info;
                    button.HoverState.FillColor = Color.FromArgb(34, 211, 238);
                    button.ForeColor = Color.White;
                    break;

                case ModernButtonStyle.Gradient:
                    button.UseTransparentBackground = true;
                    button.FillColor = Color.Transparent;
                    button.ForeColor = Color.White;
                    // Custom gradient paint
                    button.Paint += (s, e) =>
                    {
                        var rect = new Rectangle(0, 0, button.Width, button.Height);
                        using (var brush = new LinearGradientBrush(rect,
                               ThemeManager.Colors.Accent, ThemeManager.Colors.AccentLight,
                               LinearGradientMode.Horizontal))
                        {
                            e.Graphics.FillRoundedRectangle(brush, rect, button.BorderRadius);
                        }
                    };
                    break;
            }
        }

        // Modern Input Field
        public static Guna2TextBox CreateModernInput(string placeholder = "", bool isPassword = false)
        {
            var input = new Guna2TextBox
            {
                PlaceholderText = placeholder,
                Font = ResponsiveManager.Fonts.Input,
                BorderRadius = ResponsiveManager.Dimensions.BorderRadius,
                Size = new Size(250, ResponsiveManager.Dimensions.InputHeight),
                FillColor = ThemeManager.Colors.InputBackground,
                BorderColor = ThemeManager.Colors.InputBorder,
                ForeColor = ThemeManager.Colors.Text,
                PlaceholderForeColor = ThemeManager.Colors.InputPlaceholder,
                UseSystemPasswordChar = isPassword,
                RightToLeft = RightToLeft.Yes,
                TextAlign = HorizontalAlignment.Right
            };

            // Focus effects
            input.Enter += (s, e) =>
            {
                input.BorderColor = ThemeManager.Colors.InputFocus;
                input.BorderThickness = 2;
            };

            input.Leave += (s, e) =>
            {
                input.BorderColor = ThemeManager.Colors.InputBorder;
                input.BorderThickness = 1;
            };

            return input;
        }

        // Modern Group Box
        public static Guna2GroupBox CreateModernGroupBox(string title, Color? borderColor = null)
        {
            var groupBox = new Guna2GroupBox
            {
                Text = title,
                Font = ResponsiveManager.Fonts.Subtitle,
                ForeColor = ThemeManager.Colors.Text,
                CustomBorderColor = borderColor ?? ThemeManager.Colors.Accent,
                BorderColor = borderColor ?? ThemeManager.Colors.Accent,
                BorderRadius = ResponsiveManager.Dimensions.BorderRadius,
                Padding = new Padding(ResponsiveManager.Dimensions.Padding),
                RightToLeft = RightToLeft.Yes
            };

            return groupBox;
        }

        // Modern Progress Bar
        public static Guna2ProgressBar CreateModernProgressBar(int value = 0, Color? fillColor = null)
        {
            var progressBar = new Guna2ProgressBar
            {
                Value = value,
                FillColor = fillColor ?? ThemeManager.Colors.Accent,
                ProgressColor = fillColor ?? ThemeManager.Colors.Accent,
                ProgressColor2 = ThemeManager.Colors.AccentLight,
                BorderRadius = ResponsiveManager.Dimensions.SmallBorderRadius,
                Size = new Size(200, 20),
                RightToLeft = RightToLeft.Yes
            };

            return progressBar;
        }

        // Modern Toggle Switch
        public static Guna2ToggleSwitch CreateModernToggle(bool isChecked = false)
        {
            var toggle = new Guna2ToggleSwitch
            {
                Checked = isChecked,
                CheckedState =
                {
                    BorderColor = ThemeManager.Colors.Accent,
                    FillColor = ThemeManager.Colors.Accent,
                    InnerBorderColor = Color.White,
                    InnerColor = Color.White
                },
                UncheckedState =
                {
                    BorderColor = ThemeManager.Colors.Surface,
                    FillColor = ThemeManager.Colors.Surface,
                    InnerBorderColor = ThemeManager.Colors.TextMuted,
                    InnerColor = ThemeManager.Colors.TextMuted
                },
                RightToLeft = RightToLeft.Yes
            };

            return toggle;
        }

        // Modern Data Grid
        public static Guna2DataGridView CreateModernDataGrid()
        {
            var grid = new Guna2DataGridView
            {
                BackgroundColor = ThemeManager.Colors.Background,
                GridColor = ThemeManager.Colors.Border,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                RowHeadersVisible = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowTemplate = { Height = ResponsiveManager.Dimensions.GridRowHeight },
                RightToLeft = RightToLeft.Yes
            };

            // Apply modern styling
            grid.DefaultCellStyle.BackColor = ThemeManager.Effects.DataGrid.RowBackground;
            grid.DefaultCellStyle.ForeColor = ThemeManager.Colors.Text;
            grid.DefaultCellStyle.SelectionBackColor = ThemeManager.Effects.DataGrid.SelectionBackground;
            grid.DefaultCellStyle.SelectionForeColor = ThemeManager.Colors.Text;
            grid.DefaultCellStyle.Font = ResponsiveManager.Fonts.DataGrid;
            grid.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            grid.ColumnHeadersDefaultCellStyle.BackColor = ThemeManager.Effects.DataGrid.HeaderBackground;
            grid.ColumnHeadersDefaultCellStyle.ForeColor = ThemeManager.Colors.Text;
            grid.ColumnHeadersDefaultCellStyle.Font = ResponsiveManager.Fonts.DataGridHeader;
            grid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            grid.ColumnHeadersHeight = ResponsiveManager.Dimensions.GridHeaderHeight;

            grid.AlternatingRowsDefaultCellStyle.BackColor = ThemeManager.Effects.DataGrid.AlternateRowBackground;

            return grid;
        }

        // Modern Notification/Toast
        public static void ShowModernNotification(string message, NotificationType type = NotificationType.Info, int duration = 3000)
        {
            var notification = new Form
            {
                FormBorderStyle = FormBorderStyle.None,
                StartPosition = FormStartPosition.Manual,
                Size = new Size(350, 80),
                BackColor = GetNotificationColor(type),
                ShowInTaskbar = false,
                TopMost = true
            };

            var messageLabel = new Label
            {
                Text = message,
                Font = ResponsiveManager.Fonts.Regular,
                ForeColor = Color.White,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            notification.Controls.Add(messageLabel);

            // Position at top-right of screen
            var screen = Screen.PrimaryScreen.WorkingArea;
            notification.Location = new Point(screen.Right - notification.Width - 20, screen.Top + 20);

            notification.Show();

            // Auto-hide after duration
            var timer = new System.Windows.Forms.Timer { Interval = duration };
            timer.Tick += (s, e) =>
            {
                timer.Stop();
                notification.Close();
            };
            timer.Start();
        }

        public enum NotificationType
        {
            Info,
            Success,
            Warning,
            Error
        }

        private static Color GetNotificationColor(NotificationType type) => type switch
        {
            NotificationType.Success => ThemeManager.Colors.Success,
            NotificationType.Warning => ThemeManager.Colors.Warning,
            NotificationType.Error => ThemeManager.Colors.Error,
            _ => ThemeManager.Colors.Info
        };
    }

    // Extension methods for Graphics
    public static class GraphicsExtensions
    {
        public static void FillRoundedRectangle(this Graphics graphics, Brush brush, Rectangle rect, int radius)
        {
            using (var path = new GraphicsPath())
            {
                path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
                path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
                path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
                path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
                path.CloseAllFigures();
                graphics.FillPath(brush, path);
            }
        }
    }
}
