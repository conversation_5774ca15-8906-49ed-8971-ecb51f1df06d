using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class PayrollManagementForm : Form
    {
        private Guna2DataGridView grid;
        private Guna2ComboBox monthSelector;
        private Guna2GroupBox summaryGroup;

        public PayrollManagementForm()
        {
            InitializeComponent();
            InitializeSalaries();
        }

        private void InitializeSalaries()
        {
            // Create container panel for proper layout
            var containerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Initialize top panel for controls
            var topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60
            };

            // Initialize month selector
            monthSelector = new Guna2ComboBox
            {
                Size = new Size(200, 40),
                Location = new Point(20, 10),
                FillColor = Color.FromArgb(37, 36, 81),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                BorderRadius = 5
            };
            monthSelector.Items.AddRange(new string[] { 
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", 
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر" 
            });
            monthSelector.SelectedIndex = DateTime.Now.Month - 1;
            topPanel.Controls.Add(monthSelector);

            // Initialize process button
            var processButton = new Guna2Button
            {
                Text = "معالجة الرواتب",
                FillColor = Color.FromArgb(0, 126, 249),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Size = new Size(150, 40),
                Location = new Point(240, 10),
                BorderRadius = 5,
                Cursor = Cursors.Hand
            };
            topPanel.Controls.Add(processButton);

            // Initialize summary panel
            summaryGroup = new Guna2GroupBox
            {
                Text = "ملخص الرواتب",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(0, 126, 249),
                BorderColor = Color.FromArgb(0, 126, 249),
                Size = new Size(250, 150),
                Location = new Point(530, 10),
                Dock = DockStyle.Right
            };

            var totalLabel = new Label
            {
                Text = "إجمالي الرواتب: 34,500 ريال",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F),
                Location = new Point(20, 50),
                AutoSize = true
            };

            var pendingLabel = new Label
            {
                Text = "رواتب معلقة: 9,100 ريال",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F),
                Location = new Point(20, 80),
                AutoSize = true
            };

            summaryGroup.Controls.AddRange(new Control[] { totalLabel, pendingLabel });
            topPanel.Controls.Add(summaryGroup);

            // Initialize grid
            grid = new Guna2DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.FromArgb(34, 33, 74),
                ForeColor = Color.White,
                ThemeStyle = {
                    HeaderStyle = { 
                        BackColor = Color.FromArgb(37, 36, 81), 
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                    },
                    RowsStyle = { 
                        BackColor = Color.FromArgb(34, 33, 74), 
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 9F)
                    },
                    AlternatingRowsStyle = {
                        BackColor = Color.FromArgb(31, 30, 68),
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 9F)
                    }
                },
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                GridColor = Color.FromArgb(50, 49, 90),
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                DefaultCellStyle = { SelectionBackColor = Color.FromArgb(37, 36, 81) },
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RightToLeft = RightToLeft.Yes
            };

            grid.Columns.Add("EmployeeId", "الرقم الوظيفي");
            grid.Columns.Add("Name", "اسم الموظف");
            grid.Columns.Add("BasicSalary", "الراتب الأساسي");
            grid.Columns.Add("Allowances", "البدلات");
            grid.Columns.Add("Deductions", "الاستقطاعات");
            grid.Columns.Add("NetSalary", "صافي الراتب");
            grid.Columns.Add("Status", "الحالة");

            // Add sample data
            grid.Rows.Add("1001", "أحمد محمد", "10,000 ريال", "2,000 ريال", "500 ريال", "11,500 ريال", "تم الصرف");
            grid.Rows.Add("1002", "سارة أحمد", "12,000 ريال", "2,500 ريال", "600 ريال", "13,900 ريال", "تم الصرف");
            grid.Rows.Add("1003", "محمد علي", "8,000 ريال", "1,500 ريال", "400 ريال", "9,100 ريال", "معلق");
            grid.Rows.Add("1004", "فاطمة خالد", "11,000 ريال", "2,200 ريال", "550 ريال", "12,650 ريال", "تم الصرف");
            grid.Rows.Add("1005", "عمر يوسف", "9,500 ريال", "1,900 ريال", "475 ريال", "10,925 ريال", "تم الصرف");

            foreach (DataGridViewColumn column in grid.Columns)
            {
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
            }

            // Add controls to container in correct order
            containerPanel.Controls.Add(grid);      // Add grid first (will be at bottom/fill)
            containerPanel.Controls.Add(topPanel);  // Add top panel last (will be at top)

            // Add container to form
            this.Controls.Add(containerPanel);
        }
    }
}
