using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class PayrollManagementForm : Form
    {
        private Guna2DataGridView grid;
        private Guna2ComboBox monthSelector;
        private Guna2GroupBox summaryGroup;

        public PayrollManagementForm()
        {
            InitializeComponent();
            InitializeSalaries();
        }

        private void InitializeSalaries()
        {
            // Create container panel for proper layout
            var containerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Initialize top panel for controls
            var topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 120,
                BackColor = Color.FromArgb(40, 39, 84),
                Padding = new Padding(15)
            };

            // إضافة تسمية للشهر
            var monthLabel = new Label
            {
                Text = "اختر الشهر:",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(20, 10),
                AutoSize = true
            };

            // Initialize month selector
            monthSelector = new Guna2ComboBox
            {
                Size = new Size(200, 40),
                Location = new Point(20, 35),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                BorderRadius = 8,
                BorderColor = Color.FromArgb(70, 69, 114)
            };
            monthSelector.Items.AddRange(new string[] {
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            });
            monthSelector.SelectedIndex = DateTime.Now.Month - 1;

            // إضافة مجموعة أزرار الإجراءات
            var actionsPanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 600,
                Padding = new Padding(10)
            };

            // Initialize process button
            var processButton = new Guna2Button
            {
                Text = "معالجة الرواتب",
                FillColor = Color.FromArgb(40, 167, 69),
                HoverState = { FillColor = Color.FromArgb(34, 139, 58) },
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(120, 40),
                Location = new Point(470, 35),
                BorderRadius = 8,
                Cursor = Cursors.Hand
            };

            var calculateButton = new Guna2Button
            {
                Text = "حساب الرواتب",
                FillColor = Color.FromArgb(0, 123, 255),
                HoverState = { FillColor = Color.FromArgb(0, 105, 217) },
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(120, 40),
                Location = new Point(340, 35),
                BorderRadius = 8,
                Cursor = Cursors.Hand
            };

            var exportButton = new Guna2Button
            {
                Text = "تصدير كشوف",
                FillColor = Color.FromArgb(108, 117, 125),
                HoverState = { FillColor = Color.FromArgb(90, 98, 104) },
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(120, 40),
                Location = new Point(210, 35),
                BorderRadius = 8,
                Cursor = Cursors.Hand
            };

            var printButton = new Guna2Button
            {
                Text = "طباعة",
                FillColor = Color.FromArgb(102, 16, 242),
                HoverState = { FillColor = Color.FromArgb(85, 13, 202) },
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(100, 40),
                Location = new Point(100, 35),
                BorderRadius = 8,
                Cursor = Cursors.Hand
            };

            var approveButton = new Guna2Button
            {
                Text = "اعتماد",
                FillColor = Color.FromArgb(255, 193, 7),
                HoverState = { FillColor = Color.FromArgb(227, 172, 6) },
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(80, 40),
                Location = new Point(10, 35),
                BorderRadius = 8,
                Cursor = Cursors.Hand,
                ForeColor = Color.Black
            };

            actionsPanel.Controls.AddRange(new Control[] {
                processButton, calculateButton, exportButton, printButton, approveButton
            });

            topPanel.Controls.Add(monthLabel);
            topPanel.Controls.Add(monthSelector);
            topPanel.Controls.Add(actionsPanel);

            // Initialize summary panel
            summaryGroup = new Guna2GroupBox
            {
                Text = "ملخص الرواتب",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(0, 126, 249),
                BorderColor = Color.FromArgb(0, 126, 249),
                Size = new Size(250, 150),
                Location = new Point(530, 10),
                Dock = DockStyle.Right
            };

            var totalLabel = new Label
            {
                Text = "إجمالي الرواتب: 34,500 ريال",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F),
                Location = new Point(20, 50),
                AutoSize = true
            };

            var pendingLabel = new Label
            {
                Text = "رواتب معلقة: 9,100 ريال",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F),
                Location = new Point(20, 80),
                AutoSize = true
            };

            summaryGroup.Controls.AddRange(new Control[] { totalLabel, pendingLabel });
            topPanel.Controls.Add(summaryGroup);

            // Initialize grid
            grid = new Guna2DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.FromArgb(34, 33, 74),
                ForeColor = Color.White,
                ThemeStyle = {
                    HeaderStyle = {
                        BackColor = Color.FromArgb(37, 36, 81),
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                    },
                    RowsStyle = {
                        BackColor = Color.FromArgb(34, 33, 74),
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 9F)
                    },
                    AlternatingRowsStyle = {
                        BackColor = Color.FromArgb(31, 30, 68),
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 9F)
                    }
                },
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                GridColor = Color.FromArgb(50, 49, 90),
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                DefaultCellStyle = { SelectionBackColor = Color.FromArgb(37, 36, 81) },
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RightToLeft = RightToLeft.Yes
            };

            grid.Columns.Add("EmployeeId", "الرقم الوظيفي");
            grid.Columns.Add("Name", "اسم الموظف");
            grid.Columns.Add("BasicSalary", "الراتب الأساسي");
            grid.Columns.Add("NetSalary", "صافي الراتب");
            grid.Columns.Add("Status", "الحالة");
            grid.Columns.Add("PaymentDate", "تاريخ الصرف");
            grid.Columns.Add("Actions", "الإجراءات");

            // تخصيص عرض الأعمدة
            grid.Columns["EmployeeId"].Width = 120;
            grid.Columns["Name"].Width = 200;
            grid.Columns["BasicSalary"].Width = 150;
            grid.Columns["NetSalary"].Width = 150;
            grid.Columns["Status"].Width = 150;
            grid.Columns["PaymentDate"].Width = 150;
            grid.Columns["Actions"].Width = 150;

            // تحسين ارتفاع الصفوف والخط
            grid.RowTemplate.Height = 55;
            grid.ColumnHeadersHeight = 50;
            grid.DefaultCellStyle.Font = new Font("Segoe UI", 11F);
            grid.DefaultCellStyle.Padding = new Padding(8, 5, 8, 5);
            grid.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grid.ColumnHeadersDefaultCellStyle.Padding = new Padding(5);
            grid.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            grid.AllowUserToResizeColumns = true;

            // Add sample data
            grid.Rows.Add("1001", "أحمد محمد علي", "12,000 ريال", "14,200 ريال", "✅ تم الصرف", "2025-05-25", "عرض التفاصيل");
            grid.Rows.Add("1002", "سارة أحمد خالد", "15,000 ريال", "16,750 ريال", "✅ تم الصرف", "2025-05-25", "عرض التفاصيل");
            grid.Rows.Add("1003", "محمد علي حسن", "10,000 ريال", "11,400 ريال", "⏳ معلق", "--", "عرض التفاصيل");
            grid.Rows.Add("1004", "فاطمة خالد عمر", "14,000 ريال", "16,100 ريال", "✅ تم الصرف", "2025-05-25", "عرض التفاصيل");
            grid.Rows.Add("1005", "عمر يوسف سالم", "11,000 ريال", "12,650 ريال", "✅ تم الصرف", "2025-05-25", "عرض التفاصيل");
            grid.Rows.Add("1006", "نورا حسام الدين", "9,500 ريال", "11,025 ريال", "✅ تم الصرف", "2025-05-25", "عرض التفاصيل");
            grid.Rows.Add("1007", "خالد عبدالله محمد", "8,000 ريال", "9,800 ريال", "🔍 قيد المراجعة", "--", "عرض التفاصيل");
            grid.Rows.Add("1008", "ريم محمد أحمد", "9,000 ريال", "10,150 ريال", "✅ تم الصرف", "2025-05-25", "عرض التفاصيل");
            grid.Rows.Add("1009", "يوسف علي حسن", "8,500 ريال", "9,950 ريال", "⏳ معلق", "--", "عرض التفاصيل");
            grid.Rows.Add("1010", "مريم سالم أحمد", "7,500 ريال", "8,750 ريال", "✅ تم الصرف", "2025-05-25", "عرض التفاصيل");

            foreach (DataGridViewColumn column in grid.Columns)
            {
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
            }

            // Add controls to container in correct order
            containerPanel.Controls.Add(grid);      // Add grid first (will be at bottom/fill)
            containerPanel.Controls.Add(topPanel);  // Add top panel last (will be at top)

            // Add container to form
            this.Controls.Add(containerPanel);
        }
    }
}
