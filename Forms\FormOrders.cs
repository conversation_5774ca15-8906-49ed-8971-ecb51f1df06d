using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class FormOrders : Form
    {
        public FormOrders()
        {
            InitializeComponent();
            InitializeOrders();
        }

        private void InitializeOrders()
        {
            var grid = new Guna2DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.FromArgb(34, 33, 74),
                ThemeStyle = {
                    HeaderStyle = { BackColor = Color.FromArgb(37, 36, 81), ForeColor = Color.White },
                    RowsStyle = { BackColor = Color.FromArgb(34, 33, 74), ForeColor = Color.White }
                }
            };

            grid.Columns.Add("OrderId", "رقم الطلب");
            grid.Columns.Add("CustomerName", "اسم العميل");
            grid.Columns.Add("Amount", "المبلغ");
            grid.Columns.Add("Date", "التاريخ");

            // Add sample data
            grid.Rows.Add("1001", "أحمد محمد", "500 ريال", "2025-05-21");
            grid.Rows.Add("1002", "سارة أحمد", "750 ريال", "2025-05-20");
            grid.Rows.Add("1003", "محمد علي", "1200 ريال", "2025-05-19");

            this.Controls.Add(grid);
        }
    }
}