using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class SystemSettingsForm : Form
    {
        public SystemSettingsForm()
        {
            InitializeComponent();
            InitializeSettings();
        }

        private void InitializeSettings()
        {
            var containerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Theme Settings Group
            var themeGroup = new Guna2GroupBox
            {
                Text = "إعدادات المظهر",
                Font = Theme.ThemeManager.Fonts.Subtitle,
                ForeColor = Theme.ThemeManager.Colors.Text,
                CustomBorderColor = Theme.ThemeManager.Colors.Accent,
                BorderColor = Theme.ThemeManager.Colors.Border,
                Size = new Size(350, 180),
                Location = new Point(20, 20),
                Dock = DockStyle.Top,
                Padding = new Padding(10)
            };

            var themeToggle = Theme.ThemeManager.CreateFluentButton();
            themeToggle.Text = Theme.ThemeManager.IsDarkMode ? "تفعيل المظهر الفاتح" : "تفعيل المظهر الداكن";
            themeToggle.Size = new Size(200, 45);
            themeToggle.Location = new Point(20, 60);
            themeToggle.Click += (s, e) => {
                Theme.ThemeManager.ToggleTheme();
                themeToggle.Text = Theme.ThemeManager.IsDarkMode ? "تفعيل المظهر الفاتح" : "تفعيل المظهر الداكن";
            };

            var themeDescription = new Label
            {
                Text = "يمكنك التبديل بين المظهر الفاتح والداكن حسب تفضيلاتك",
                ForeColor = Theme.ThemeManager.Colors.Text,
                Font = Theme.ThemeManager.Fonts.Regular,
                Location = new Point(20, 120),
                AutoSize = true
            };

            themeGroup.Controls.Add(themeToggle);
            themeGroup.Controls.Add(themeDescription);

            // Company Settings Group
            var companyGroup = new Guna2GroupBox
            {
                Text = "إعدادات الشركة",
                Font = Theme.ThemeManager.Fonts.Subtitle,
                ForeColor = Theme.ThemeManager.Colors.Text,
                CustomBorderColor = Theme.ThemeManager.Colors.Accent,
                BorderColor = Theme.ThemeManager.Colors.Border,
                Size = new Size(350, 250),
                Location = new Point(20, 220),
                Padding = new Padding(10)
            };

            var workHoursInput = Theme.ThemeManager.CreateFluentTextBox();
            workHoursInput.PlaceholderText = "ساعات العمل اليومية";
            workHoursInput.Text = "8";
            workHoursInput.Size = new Size(150, 36);
            workHoursInput.Location = new Point(20, 60);

            var workDaysInput = Theme.ThemeManager.CreateFluentTextBox();
            workDaysInput.PlaceholderText = "أيام العمل في الأسبوع";
            workDaysInput.Text = "5";
            workDaysInput.Size = new Size(150, 36);
            workDaysInput.Location = new Point(20, 120);

            var saveButton = Theme.ThemeManager.CreateFluentButton();
            saveButton.Text = "حفظ التغييرات";
            saveButton.Size = new Size(150, 45);
            saveButton.Location = new Point(20, 180);

            companyGroup.Controls.Add(workHoursInput);
            companyGroup.Controls.Add(workDaysInput);
            companyGroup.Controls.Add(saveButton);

            containerPanel.Controls.Add(companyGroup);
            containerPanel.Controls.Add(themeGroup);

            Controls.Add(containerPanel);

            // Subscribe to theme changes
            Theme.ThemeManager.ThemeChanged += (s, e) => 
            {
                themeToggle.Text = Theme.ThemeManager.IsDarkMode ? "تفعيل المظهر الفاتح" : "تفعيل المظهر الداكن";
                Theme.ThemeManager.ApplyTheme(this);
            };
        }
    }
}
