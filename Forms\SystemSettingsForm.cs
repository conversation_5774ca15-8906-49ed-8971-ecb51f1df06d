using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class SystemSettingsForm : Form
    {
        public SystemSettingsForm()
        {
            InitializeComponent();
            InitializeSettings();
        }

        private void InitializeSettings()
        {
            var containerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                AutoScroll = true
            };

            // إنشاء تخطيط بثلاثة أعمدة
            var leftPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 350,
                Padding = new Padding(10)
            };

            var centerPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 350,
                Padding = new Padding(10)
            };

            var rightPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            // Theme Settings Group
            var themeGroup = new Guna2GroupBox
            {
                Text = "إعدادات المظهر",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(0, 126, 249),
                BorderColor = Color.FromArgb(0, 126, 249),
                Size = new Size(320, 200),
                Location = new Point(10, 10),
                Padding = new Padding(15)
            };

            var themeToggle = new Guna2Button
            {
                Text = "تفعيل المظهر الفاتح",
                Size = new Size(200, 45),
                Location = new Point(20, 60),
                FillColor = Color.FromArgb(0, 126, 249),
                HoverState = { FillColor = Color.FromArgb(0, 105, 217) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };

            var languageCombo = new Guna2ComboBox
            {
                Size = new Size(200, 40),
                Location = new Point(20, 120),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                BorderRadius = 8
            };
            languageCombo.Items.AddRange(new string[] { "العربية", "English" });
            languageCombo.SelectedIndex = 0;

            var languageLabel = new Label
            {
                Text = "اللغة:",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(20, 100),
                AutoSize = true
            };

            // Font Settings Button
            var fontSettingsButton = new Guna2Button
            {
                Text = "إعدادات الخطوط والأحجام",
                Size = new Size(280, 45),
                Location = new Point(20, 170),
                FillColor = Color.FromArgb(52, 152, 219),
                HoverState = { FillColor = Color.FromArgb(41, 128, 185) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };

            fontSettingsButton.Click += (s, e) =>
            {
                var fontForm = new FontSettingsForm();
                fontForm.ShowDialog();
            };

            // Theme Selection Button
            var themeSelectionButton = new Guna2Button
            {
                Text = "اختيار الثيم العصري",
                Size = new Size(280, 45),
                Location = new Point(20, 225),
                FillColor = Color.FromArgb(155, 89, 182),
                HoverState = { FillColor = Color.FromArgb(142, 68, 173) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };

            themeSelectionButton.Click += (s, e) =>
            {
                var themeForm = new ThemeSelectionForm();
                themeForm.ShowDialog();
            };

            themeGroup.Controls.Add(themeToggle);
            themeGroup.Controls.Add(languageLabel);
            themeGroup.Controls.Add(languageCombo);
            themeGroup.Controls.Add(fontSettingsButton);
            themeGroup.Controls.Add(themeSelectionButton);

            // Company Settings Group
            var companyGroup = new Guna2GroupBox
            {
                Text = "إعدادات الشركة",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(40, 167, 69),
                BorderColor = Color.FromArgb(40, 167, 69),
                Size = new Size(320, 280),
                Location = new Point(10, 220),
                Padding = new Padding(15)
            };

            var companyNameInput = new Guna2TextBox
            {
                PlaceholderText = "اسم الشركة",
                Text = "شركة موافق للتقنية",
                Size = new Size(280, 40),
                Location = new Point(20, 40),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White
            };

            var workHoursInput = new Guna2TextBox
            {
                PlaceholderText = "ساعات العمل اليومية",
                Text = "8",
                Size = new Size(130, 40),
                Location = new Point(20, 100),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White
            };

            var workDaysInput = new Guna2TextBox
            {
                PlaceholderText = "أيام العمل في الأسبوع",
                Text = "5",
                Size = new Size(130, 40),
                Location = new Point(170, 100),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White
            };

            var startTimeInput = new Guna2TextBox
            {
                PlaceholderText = "وقت بداية العمل",
                Text = "08:00",
                Size = new Size(130, 40),
                Location = new Point(20, 160),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White
            };

            var endTimeInput = new Guna2TextBox
            {
                PlaceholderText = "وقت نهاية العمل",
                Text = "16:00",
                Size = new Size(130, 40),
                Location = new Point(170, 160),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White
            };

            var saveCompanyButton = new Guna2Button
            {
                Text = "حفظ إعدادات الشركة",
                Size = new Size(280, 40),
                Location = new Point(20, 220),
                FillColor = Color.FromArgb(40, 167, 69),
                HoverState = { FillColor = Color.FromArgb(34, 139, 58) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };

            companyGroup.Controls.AddRange(new Control[] {
                companyNameInput, workHoursInput, workDaysInput,
                startTimeInput, endTimeInput, saveCompanyButton
            });

            // Security Settings Group
            var securityGroup = new Guna2GroupBox
            {
                Text = "إعدادات الأمان",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(220, 53, 69),
                BorderColor = Color.FromArgb(220, 53, 69),
                Size = new Size(320, 200),
                Location = new Point(10, 10),
                Padding = new Padding(15)
            };

            var passwordPolicyCheck = new Guna2CheckBox
            {
                Text = "تفعيل سياسة كلمات المرور القوية",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                Location = new Point(20, 40),
                AutoSize = true,
                Checked = true
            };

            var sessionTimeoutInput = new Guna2TextBox
            {
                PlaceholderText = "مهلة انتهاء الجلسة (بالدقائق)",
                Text = "30",
                Size = new Size(200, 40),
                Location = new Point(20, 80),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White
            };

            var backupCheck = new Guna2CheckBox
            {
                Text = "تفعيل النسخ الاحتياطي التلقائي",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                Location = new Point(20, 130),
                AutoSize = true,
                Checked = true
            };

            securityGroup.Controls.AddRange(new Control[] {
                passwordPolicyCheck, sessionTimeoutInput, backupCheck
            });

            // Notification Settings Group
            var notificationGroup = new Guna2GroupBox
            {
                Text = "إعدادات التنبيهات",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(255, 193, 7),
                BorderColor = Color.FromArgb(255, 193, 7),
                Size = new Size(320, 280),
                Location = new Point(10, 220),
                Padding = new Padding(15)
            };

            var emailNotifications = new Guna2CheckBox
            {
                Text = "تنبيهات البريد الإلكتروني",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                Location = new Point(20, 40),
                AutoSize = true,
                Checked = true
            };

            var smsNotifications = new Guna2CheckBox
            {
                Text = "تنبيهات الرسائل النصية",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                Location = new Point(20, 80),
                AutoSize = true
            };

            var attendanceAlerts = new Guna2CheckBox
            {
                Text = "تنبيهات الحضور والغياب",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                Location = new Point(20, 120),
                AutoSize = true,
                Checked = true
            };

            var salaryAlerts = new Guna2CheckBox
            {
                Text = "تنبيهات معالجة الرواتب",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                Location = new Point(20, 160),
                AutoSize = true,
                Checked = true
            };

            var saveNotificationButton = new Guna2Button
            {
                Text = "حفظ إعدادات التنبيهات",
                Size = new Size(280, 40),
                Location = new Point(20, 220),
                FillColor = Color.FromArgb(255, 193, 7),
                HoverState = { FillColor = Color.FromArgb(227, 172, 6) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand,
                ForeColor = Color.Black
            };

            notificationGroup.Controls.AddRange(new Control[] {
                emailNotifications, smsNotifications, attendanceAlerts,
                salaryAlerts, saveNotificationButton
            });

            // إضافة المجموعات إلى الأعمدة
            leftPanel.Controls.Add(themeGroup);
            leftPanel.Controls.Add(companyGroup);
            centerPanel.Controls.Add(securityGroup);
            centerPanel.Controls.Add(notificationGroup);

            containerPanel.Controls.Add(rightPanel);
            containerPanel.Controls.Add(centerPanel);
            containerPanel.Controls.Add(leftPanel);

            Controls.Add(containerPanel);
        }
    }
}
