using System.Drawing;
using System.Windows.Forms;

namespace ModernWinApp.Theme
{
    public static class ResponsiveManager
    {
        public enum ScreenSize
        {
            Small,      // < 1366x768
            Medium,     // 1366x768 - 1920x1080
            Large,      // 1920x1080 - 2560x1440
            ExtraLarge  // > 2560x1440
        }

        public static ScreenSize CurrentScreenSize { get; private set; }
        public static float ScaleFactor { get; private set; } = 1.0f;
        
        // Arabic font settings
        public static string ArabicFontFamily { get; set; } = "Segoe UI";
        public static string ArabicFallbackFont { get; set; } = "Tahoma";

        static ResponsiveManager()
        {
            UpdateScreenSize();
        }

        public static void UpdateScreenSize()
        {
            var screen = Screen.PrimaryScreen;
            var width = screen.Bounds.Width;
            var height = screen.Bounds.Height;

            if (width < 1366 || height < 768)
            {
                CurrentScreenSize = ScreenSize.Small;
                ScaleFactor = 0.8f;
            }
            else if (width <= 1920 && height <= 1080)
            {
                CurrentScreenSize = ScreenSize.Medium;
                ScaleFactor = 1.0f;
            }
            else if (width <= 2560 && height <= 1440)
            {
                CurrentScreenSize = ScreenSize.Large;
                ScaleFactor = 1.3f;
            }
            else
            {
                CurrentScreenSize = ScreenSize.ExtraLarge;
                ScaleFactor = 1.6f;
            }
        }

        public static class Fonts
        {
            public static Font GetArabicFont(float baseSize, FontStyle style = FontStyle.Regular)
            {
                var scaledSize = baseSize * ScaleFactor;
                
                try
                {
                    return new Font(ArabicFontFamily, scaledSize, style);
                }
                catch
                {
                    return new Font(ArabicFallbackFont, scaledSize, style);
                }
            }

            // Arabic UI Fonts
            public static Font ExtraLargeTitle => GetArabicFont(24f, FontStyle.Bold);
            public static Font LargeTitle => GetArabicFont(20f, FontStyle.Bold);
            public static Font Title => GetArabicFont(16f, FontStyle.Bold);
            public static Font Subtitle => GetArabicFont(14f, FontStyle.Bold);
            public static Font Regular => GetArabicFont(12f, FontStyle.Regular);
            public static Font Small => GetArabicFont(10f, FontStyle.Regular);
            public static Font ExtraSmall => GetArabicFont(9f, FontStyle.Regular);

            // Menu and Button Fonts
            public static Font MenuButton => GetArabicFont(13f, FontStyle.Regular);
            public static Font MenuTitle => GetArabicFont(18f, FontStyle.Bold);
            
            // Data Display Fonts
            public static Font DataGrid => GetArabicFont(11f, FontStyle.Regular);
            public static Font DataGridHeader => GetArabicFont(12f, FontStyle.Bold);
            
            // Form Input Fonts
            public static Font Input => GetArabicFont(11f, FontStyle.Regular);
            public static Font Label => GetArabicFont(11f, FontStyle.Regular);
        }

        public static class Dimensions
        {
            private static int Scale(int baseValue) => (int)(baseValue * ScaleFactor);

            // Layout Dimensions
            public static int SidebarWidth => Scale(280);
            public static int TopBarHeight => Scale(80);
            public static int Padding => Scale(20);
            public static int SmallPadding => Scale(10);
            public static int LargePadding => Scale(30);

            // Control Dimensions
            public static int ButtonHeight => Scale(45);
            public static int LargeButtonHeight => Scale(55);
            public static int InputHeight => Scale(40);
            public static int MenuButtonHeight => Scale(60);

            // Border and Radius
            public static int BorderRadius => Scale(8);
            public static int SmallBorderRadius => Scale(4);
            public static int LargeBorderRadius => Scale(12);

            // Grid Dimensions
            public static int GridRowHeight => Scale(50);
            public static int GridHeaderHeight => Scale(55);

            // Icon Sizes
            public static int SmallIconSize => Scale(16);
            public static int MediumIconSize => Scale(20);
            public static int LargeIconSize => Scale(24);
            public static int ExtraLargeIconSize => Scale(32);

            // Minimum Window Size
            public static int MinimumWidth => Scale(1200);
            public static int MinimumHeight => Scale(700);

            // Card and Panel Dimensions
            public static int CardWidth => Scale(300);
            public static int CardHeight => Scale(200);
            public static int PanelSpacing => Scale(15);
        }

        public static class RTLSettings
        {
            public static RightToLeft Direction => RightToLeft.Yes;
            public static bool LayoutDirection => true; // RTL Layout
            
            public static ContentAlignment GetRTLAlignment(ContentAlignment defaultAlignment)
            {
                return defaultAlignment switch
                {
                    ContentAlignment.TopLeft => ContentAlignment.TopRight,
                    ContentAlignment.TopRight => ContentAlignment.TopLeft,
                    ContentAlignment.MiddleLeft => ContentAlignment.MiddleRight,
                    ContentAlignment.MiddleRight => ContentAlignment.MiddleLeft,
                    ContentAlignment.BottomLeft => ContentAlignment.BottomRight,
                    ContentAlignment.BottomRight => ContentAlignment.BottomLeft,
                    _ => defaultAlignment
                };
            }

            public static HorizontalAlignment GetRTLTextAlignment(HorizontalAlignment defaultAlignment)
            {
                return defaultAlignment switch
                {
                    HorizontalAlignment.Left => HorizontalAlignment.Right,
                    HorizontalAlignment.Right => HorizontalAlignment.Left,
                    _ => defaultAlignment
                };
            }
        }

        public static void ApplyResponsiveDesign(Form form)
        {
            if (form == null) return;

            // Apply RTL settings
            form.RightToLeft = RTLSettings.Direction;
            form.RightToLeftLayout = RTLSettings.LayoutDirection;

            // Apply responsive dimensions
            form.MinimumSize = new Size(Dimensions.MinimumWidth, Dimensions.MinimumHeight);

            // Apply to all controls
            ApplyToControls(form.Controls);
        }

        private static void ApplyToControls(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                ApplyToControl(control);
                
                if (control.HasChildren)
                {
                    ApplyToControls(control.Controls);
                }
            }
        }

        private static void ApplyToControl(Control control)
        {
            // Apply RTL
            control.RightToLeft = RTLSettings.Direction;

            // Apply fonts based on control type
            switch (control)
            {
                case Label label:
                    if (label.Font.Style.HasFlag(FontStyle.Bold))
                        label.Font = Fonts.Subtitle;
                    else
                        label.Font = Fonts.Label;
                    label.TextAlign = RTLSettings.GetRTLAlignment(ContentAlignment.MiddleRight);
                    break;

                case Button button:
                    button.Font = Fonts.Regular;
                    button.TextAlign = RTLSettings.GetRTLAlignment(ContentAlignment.MiddleCenter);
                    break;

                case TextBox textBox:
                    textBox.Font = Fonts.Input;
                    textBox.TextAlign = RTLSettings.GetRTLTextAlignment(HorizontalAlignment.Right);
                    break;

                case Panel panel:
                    panel.Padding = new Padding(Dimensions.SmallPadding);
                    break;
            }

            // Apply Guna2 controls
            ApplyToGunaControl(control);
        }

        private static void ApplyToGunaControl(Control control)
        {
            switch (control)
            {
                case Guna.UI2.WinForms.Guna2Button gunaButton:
                    gunaButton.Font = Fonts.Regular;
                    gunaButton.BorderRadius = Dimensions.BorderRadius;
                    gunaButton.Size = new Size(gunaButton.Width, Dimensions.ButtonHeight);
                    break;

                case Guna.UI2.WinForms.Guna2TextBox gunaTextBox:
                    gunaTextBox.Font = Fonts.Input;
                    gunaTextBox.BorderRadius = Dimensions.BorderRadius;
                    gunaTextBox.Size = new Size(gunaTextBox.Width, Dimensions.InputHeight);
                    gunaTextBox.TextAlign = HorizontalAlignment.Right;
                    break;

                case Guna.UI2.WinForms.Guna2GroupBox gunaGroupBox:
                    gunaGroupBox.Font = Fonts.Subtitle;
                    gunaGroupBox.BorderRadius = Dimensions.BorderRadius;
                    break;

                case FontAwesome.Sharp.IconButton iconButton:
                    iconButton.Font = Fonts.MenuButton;
                    iconButton.IconSize = Dimensions.MediumIconSize;
                    iconButton.Size = new Size(iconButton.Width, Dimensions.MenuButtonHeight);
                    iconButton.TextAlign = RTLSettings.GetRTLAlignment(ContentAlignment.MiddleRight);
                    iconButton.ImageAlign = RTLSettings.GetRTLAlignment(ContentAlignment.MiddleLeft);
                    break;
            }
        }

        public static event EventHandler ScreenSizeChanged;

        public static void OnScreenSizeChanged()
        {
            UpdateScreenSize();
            ScreenSizeChanged?.Invoke(null, EventArgs.Empty);
        }
    }
}
