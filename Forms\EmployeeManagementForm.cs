using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class EmployeeManagementForm : Form
    {
        private Guna2DataGridView grid;
        private Guna2Button addButton;
        private Panel toolbarPanel;

        public EmployeeManagementForm()
        {
            InitializeComponent();
            InitializeEmployees();
            Theme.ThemeManager.ThemeChanged += (s, e) => Theme.ThemeManager.ApplyTheme(this);
        }

        private void InitializeEmployees()
        {
            var containerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // إنشاء شريط الأدوات العلوي
            toolbarPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                Padding = new Padding(0, 0, 0, 20),
                BackColor = Color.FromArgb(40, 39, 84)
            };

            // إنشاء مجموعة الأزرار
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 600,
                Height = 60
            };

            // زر إضافة موظف
            addButton = new Guna2Button
            {
                Text = "إضافة موظف",
                Size = new Size(120, 40),
                Location = new Point(480, 10),
                FillColor = Color.FromArgb(40, 167, 69),
                HoverState = { FillColor = Color.FromArgb(34, 139, 58) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر تعديل موظف
            var editButton = new Guna2Button
            {
                Text = "تعديل",
                Size = new Size(100, 40),
                Location = new Point(370, 10),
                FillColor = Color.FromArgb(0, 123, 255),
                HoverState = { FillColor = Color.FromArgb(0, 105, 217) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر حذف موظف
            var deleteButton = new Guna2Button
            {
                Text = "حذف",
                Size = new Size(100, 40),
                Location = new Point(260, 10),
                FillColor = Color.FromArgb(220, 53, 69),
                HoverState = { FillColor = Color.FromArgb(200, 35, 51) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر تصدير البيانات
            var exportButton = new Guna2Button
            {
                Text = "تصدير Excel",
                Size = new Size(120, 40),
                Location = new Point(130, 10),
                FillColor = Color.FromArgb(108, 117, 125),
                HoverState = { FillColor = Color.FromArgb(90, 98, 104) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر طباعة
            var printButton = new Guna2Button
            {
                Text = "طباعة",
                Size = new Size(100, 40),
                Location = new Point(20, 10),
                FillColor = Color.FromArgb(102, 16, 242),
                HoverState = { FillColor = Color.FromArgb(85, 13, 202) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // مربع البحث
            var searchBox = new Guna2TextBox
            {
                PlaceholderText = "البحث عن موظف...",
                Size = new Size(250, 40),
                Location = new Point(20, 10),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White,
                PlaceholderForeColor = Color.LightGray,
                BorderColor = Color.FromArgb(70, 69, 114)
            };

            var searchPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 280
            };
            searchPanel.Controls.Add(searchBox);

            buttonsPanel.Controls.AddRange(new Control[] {
                addButton, editButton, deleteButton, exportButton, printButton
            });

            toolbarPanel.Controls.Add(buttonsPanel);
            toolbarPanel.Controls.Add(searchPanel);

            grid = new Guna2DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Theme.ThemeManager.Colors.Secondary,
                ForeColor = Theme.ThemeManager.Colors.Text,
                ThemeStyle = {
                    HeaderStyle = {
                        Font = new Font(Theme.ThemeManager.Fonts.Subtitle.FontFamily, 11, FontStyle.Bold),
                        BackColor = Theme.ThemeManager.Effects.DataGrid.HeaderBackground,
                        ForeColor = Theme.ThemeManager.Colors.Text
                    },
                    RowsStyle = {
                        BackColor = Theme.ThemeManager.Effects.DataGrid.RowBackground,
                        Font = new Font(Theme.ThemeManager.Fonts.Regular.FontFamily, 10),
                        ForeColor = Theme.ThemeManager.Colors.Text
                    },
                    AlternatingRowsStyle = {
                        BackColor = Theme.ThemeManager.Effects.DataGrid.AlternateRowBackground,
                        Font = new Font(Theme.ThemeManager.Fonts.Regular.FontFamily, 10),
                        ForeColor = Theme.ThemeManager.Colors.Text
                    }
                },
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                GridColor = Theme.ThemeManager.Colors.Border,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                DefaultCellStyle = {
                    SelectionBackColor = Theme.ThemeManager.Effects.DataGrid.SelectionBackground,
                    SelectionForeColor = Theme.ThemeManager.Colors.Text,
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RightToLeft = RightToLeft.Yes,
                ColumnHeadersHeight = Theme.ThemeManager.Dimensions.GridHeaderHeight,
                RowTemplate = { Height = Theme.ThemeManager.Dimensions.GridRowHeight }
            };

            // إضافة الأعمدة الأساسية فقط
            grid.Columns.Add("Photo", "الصورة");
            grid.Columns.Add("Id", "الرقم الوظيفي");
            grid.Columns.Add("Name", "اسم الموظف");
            grid.Columns.Add("Department", "القسم");
            grid.Columns.Add("Position", "المنصب");
            grid.Columns.Add("Status", "الحالة");
            grid.Columns.Add("Actions", "الإجراءات");

            // تخصيص عرض الأعمدة
            grid.Columns["Photo"].Width = 80;
            grid.Columns["Id"].Width = 120;
            grid.Columns["Name"].Width = 200;
            grid.Columns["Department"].Width = 180;
            grid.Columns["Position"].Width = 200;
            grid.Columns["Status"].Width = 120;
            grid.Columns["Actions"].Width = 150;

            // تخصيص ارتفاع الصفوف لعرض أفضل
            grid.RowTemplate.Height = 60;
            grid.ColumnHeadersHeight = 50;

            // تحسين خط الجدول
            grid.DefaultCellStyle.Font = new Font("Segoe UI", 11F);
            grid.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 12F, FontStyle.Bold);

            // إضافة البيانات التجريبية المحسنة
            grid.Rows.Add("👤", "1001", "أحمد محمد علي", "تقنية المعلومات", "مطور برمجيات أول", "🟢 نشط", "عرض البروفايل");
            grid.Rows.Add("👤", "1002", "سارة أحمد خالد", "الموارد البشرية", "مدير موارد بشرية", "🟢 نشط", "عرض البروفايل");
            grid.Rows.Add("👤", "1003", "محمد علي حسن", "المالية والمحاسبة", "محاسب رئيسي", "🟡 إجازة", "عرض البروفايل");
            grid.Rows.Add("👤", "1004", "فاطمة خالد عمر", "إدارة العمليات", "مدير مشروع", "🟢 نشط", "عرض البروفايل");
            grid.Rows.Add("👤", "1005", "عمر يوسف سالم", "التسويق والمبيعات", "مسؤول تسويق رقمي", "🟢 نشط", "عرض البروفايل");
            grid.Rows.Add("👤", "1006", "نورا حسام الدين", "تقنية المعلومات", "مصمم واجهات مستخدم", "🟢 نشط", "عرض البروفايل");
            grid.Rows.Add("👤", "1007", "خالد عبدالله محمد", "المبيعات", "مندوب مبيعات أول", "🟢 نشط", "عرض البروفايل");
            grid.Rows.Add("👤", "1008", "ريم محمد أحمد", "الموارد البشرية", "أخصائي توظيف", "🟢 نشط", "عرض البروفايل");
            grid.Rows.Add("👤", "1009", "يوسف علي حسن", "تقنية المعلومات", "مهندس شبكات", "🟡 إجازة", "عرض البروفايل");
            grid.Rows.Add("👤", "1010", "مريم سالم أحمد", "خدمة العملاء", "مسؤول خدمة عملاء", "🟢 نشط", "عرض البروفايل");

            foreach (DataGridViewColumn column in grid.Columns)
            {
                column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
            }

            // إضافة معالج النقر على الجدول
            grid.CellClick += Grid_CellClick;

            containerPanel.Controls.Add(grid);
            containerPanel.Controls.Add(toolbarPanel);
            Controls.Add(containerPanel);
            Theme.ThemeManager.ApplyTheme(this);
        }

        private void Grid_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                // إذا تم النقر على عمود "الإجراءات" أو أي مكان في الصف
                if (grid.Columns[e.ColumnIndex].Name == "Actions" || e.ColumnIndex >= 0)
                {
                    var employeeId = grid.Rows[e.RowIndex].Cells["Id"].Value?.ToString();
                    var employeeName = grid.Rows[e.RowIndex].Cells["Name"].Value?.ToString();

                    if (!string.IsNullOrEmpty(employeeId))
                    {
                        // فتح نموذج بروفايل الموظف
                        var profileForm = new EmployeeProfileForm(employeeId, employeeName);
                        profileForm.ShowDialog();
                    }
                }
            }
        }
    }
}
