using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class EmployeeManagementForm : Form
    {
        private Guna2DataGridView grid;
        private Guna2Button addButton;
        private Panel toolbarPanel;

        public EmployeeManagementForm()
        {
            InitializeComponent();
            InitializeEmployees();
            Theme.ThemeManager.ThemeChanged += (s, e) => Theme.ThemeManager.ApplyTheme(this);
        }

        private void InitializeEmployees()
        {
            this.BackColor = Theme.ThemeManager.Colors.Background;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            var containerPanel = new Controls.DoubleBufferedPanel
            {
                Dock = DockStyle.Fill,
                BackColor = Theme.ThemeManager.Colors.Background
            };

            // إنشاء شريط الأدوات العلوي مع التخطيط الذكي
            toolbarPanel = new Controls.DoubleBufferedPanel
            {
                Height = Theme.ResponsiveManager.Dimensions.TopBarHeight,
                BackColor = Theme.ThemeManager.Colors.Card,
                Padding = new Padding(Theme.SmartLayoutManager.Spacing.ContainerMargin)
            };

            // إنشاء panel للبحث (يسار في RTL)
            var searchPanel = new Controls.DoubleBufferedPanel
            {
                Dock = DockStyle.Left,
                Width = (int)(320 * Theme.ResponsiveManager.ScaleFactor),
                BackColor = Color.Transparent,
                Padding = new Padding(10),
                RightToLeft = RightToLeft.Yes
            };

            // مربع البحث
            var searchBox = Theme.ModernUIComponents.CreateModernInput("البحث عن موظف...");
            searchBox.Size = new Size((int)(300 * Theme.ResponsiveManager.ScaleFactor),
                                    Theme.ResponsiveManager.Dimensions.InputHeight);
            searchBox.Anchor = AnchorStyles.Left | AnchorStyles.Top;

            // توسيط مربع البحث عمودياً وأفقياً للتخطيط العربي
            searchPanel.Resize += (s, e) =>
            {
                if (searchPanel.Height > 0 && searchPanel.Width > 0)
                {
                    var centerY = (searchPanel.Height - searchBox.Height) / 2;
                    var centerX = (searchPanel.Width - searchBox.Width) / 2;
                    searchBox.Location = new Point(centerX, centerY);
                }
            };

            searchPanel.Controls.Add(searchBox);

            // إنشاء panel للأزرار (يمين في RTL)
            var buttonsPanel = new Controls.DoubleBufferedPanel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                Padding = new Padding(20),
                RightToLeft = RightToLeft.Yes
            };

            // زر إضافة موظف
            addButton = Theme.ModernUIComponents.CreateModernButton("إضافة موظف",
                Theme.ModernUIComponents.ModernButtonStyle.Success);
            addButton.Size = new Size((int)(140 * Theme.ResponsiveManager.ScaleFactor),
                                    Theme.ResponsiveManager.Dimensions.ButtonHeight);

            // زر تعديل موظف
            var editButton = Theme.ModernUIComponents.CreateModernButton("تعديل",
                Theme.ModernUIComponents.ModernButtonStyle.Primary);
            editButton.Size = new Size((int)(120 * Theme.ResponsiveManager.ScaleFactor),
                                     Theme.ResponsiveManager.Dimensions.ButtonHeight);

            // زر حذف موظف
            var deleteButton = Theme.ModernUIComponents.CreateModernButton("حذف",
                Theme.ModernUIComponents.ModernButtonStyle.Error);
            deleteButton.Size = new Size((int)(100 * Theme.ResponsiveManager.ScaleFactor),
                                       Theme.ResponsiveManager.Dimensions.ButtonHeight);

            // زر تصدير البيانات
            var exportButton = Theme.ModernUIComponents.CreateModernButton("تصدير Excel",
                Theme.ModernUIComponents.ModernButtonStyle.Secondary);
            exportButton.Size = new Size((int)(140 * Theme.ResponsiveManager.ScaleFactor),
                                       Theme.ResponsiveManager.Dimensions.ButtonHeight);

            // زر طباعة
            var printButton = Theme.ModernUIComponents.CreateModernButton("طباعة",
                Theme.ModernUIComponents.ModernButtonStyle.Info);
            printButton.Size = new Size((int)(120 * Theme.ResponsiveManager.ScaleFactor),
                                      Theme.ResponsiveManager.Dimensions.ButtonHeight);

            // إضافة الأزرار للوحة الأزرار
            buttonsPanel.Controls.AddRange(new Control[] {
                addButton, editButton, deleteButton, exportButton, printButton
            });

            // تطبيق التخطيط الذكي للأزرار فقط
            Theme.SmartLayoutManager.ApplySmartLayout(buttonsPanel, Theme.SmartLayoutManager.LayoutMode.Distributed);

            // توسيط الأزرار عمودياً عند تغيير حجم الـ panel
            buttonsPanel.Resize += (s, e) =>
            {
                if (buttonsPanel.Height > 0)
                {
                    Theme.SmartLayoutManager.ApplySmartLayout(buttonsPanel, Theme.SmartLayoutManager.LayoutMode.Distributed);
                }
            };

            // إضافة الـ panels لشريط الأدوات (ترتيب RTL)
            toolbarPanel.Controls.Add(searchPanel);   // البحث أولاً (يسار في RTL)
            toolbarPanel.Controls.Add(buttonsPanel);  // الأزرار ثانياً (يملأ المساحة المتبقية)
            toolbarPanel.Dock = DockStyle.Top;
            toolbarPanel.RightToLeft = RightToLeft.Yes;

            // إنشاء الجدول مع التخطيط الذكي
            grid = Theme.ModernUIComponents.CreateModernDataGrid();

            // تخصيص إضافي للجدول
            grid.ColumnHeadersHeight = Theme.ResponsiveManager.Dimensions.GridHeaderHeight;
            grid.RowTemplate.Height = Theme.ResponsiveManager.Dimensions.GridRowHeight;
            grid.MultiSelect = false;

            // إضافة الأعمدة مع تخصيص مفصل
            var photoColumn = new DataGridViewTextBoxColumn
            {
                Name = "Photo",
                HeaderText = "الصورة",
                Width = 80,
                MinimumWidth = 80,
                Resizable = DataGridViewTriState.False,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 20F),
                    Padding = new Padding(5)
                }
            };

            var idColumn = new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "الرقم الوظيفي",
                Width = 130,
                MinimumWidth = 100,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                    ForeColor = Color.LightBlue
                }
            };

            var nameColumn = new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "اسم الموظف",
                Width = 220,
                MinimumWidth = 180,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                    Padding = new Padding(15, 5, 15, 5)
                }
            };

            var departmentColumn = new DataGridViewTextBoxColumn
            {
                Name = "Department",
                HeaderText = "القسم",
                Width = 200,
                MinimumWidth = 150,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11F),
                    ForeColor = Color.LightGreen,
                    Padding = new Padding(10, 5, 10, 5)
                }
            };

            var positionColumn = new DataGridViewTextBoxColumn
            {
                Name = "Position",
                HeaderText = "المنصب",
                Width = 220,
                MinimumWidth = 180,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11F),
                    ForeColor = Color.LightYellow,
                    Padding = new Padding(10, 5, 10, 5)
                }
            };

            var statusColumn = new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                Width = 130,
                MinimumWidth = 100,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                    Padding = new Padding(5)
                }
            };

            var actionsColumn = new DataGridViewTextBoxColumn
            {
                Name = "Actions",
                HeaderText = "ملف الموظف",
                Width = 120,
                MinimumWidth = 100,
                Resizable = DataGridViewTriState.False,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 18F),
                    ForeColor = Color.LightBlue,
                    Padding = new Padding(5)
                }
            };

            // إضافة الأعمدة للجدول
            grid.Columns.AddRange(new DataGridViewColumn[] {
                photoColumn, idColumn, nameColumn, departmentColumn,
                positionColumn, statusColumn, actionsColumn
            });

            // تخصيص عام لرؤوس الأعمدة
            foreach (DataGridViewColumn column in grid.Columns)
            {
                column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.HeaderCell.Style.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                column.HeaderCell.Style.ForeColor = Color.White;
                column.HeaderCell.Style.BackColor = Color.FromArgb(40, 39, 84);
                column.HeaderCell.Style.Padding = new Padding(5);
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
            }

            // إضافة البيانات التجريبية المحسنة
            AddEmployeeRow("👤", "1001", "أحمد محمد علي", "تقنية المعلومات", "مطور برمجيات أول", "🟢 نشط", "📁");
            AddEmployeeRow("👤", "1002", "سارة أحمد خالد", "الموارد البشرية", "مدير موارد بشرية", "🟢 نشط", "📁");
            AddEmployeeRow("👤", "1003", "محمد علي حسن", "المالية والمحاسبة", "محاسب رئيسي", "🟡 إجازة", "📁");
            AddEmployeeRow("👤", "1004", "فاطمة خالد عمر", "إدارة العمليات", "مدير مشروع", "🟢 نشط", "📁");
            AddEmployeeRow("👤", "1005", "عمر يوسف سالم", "التسويق والمبيعات", "مسؤول تسويق رقمي", "🟢 نشط", "📁");
            AddEmployeeRow("👤", "1006", "نورا حسام الدين", "تقنية المعلومات", "مصمم واجهات مستخدم", "🟢 نشط", "📁");
            AddEmployeeRow("👤", "1007", "خالد عبدالله محمد", "المبيعات", "مندوب مبيعات أول", "🟢 نشط", "📁");
            AddEmployeeRow("👤", "1008", "ريم محمد أحمد", "الموارد البشرية", "أخصائي توظيف", "🟢 نشط", "📁");
            AddEmployeeRow("👤", "1009", "يوسف علي حسن", "تقنية المعلومات", "مهندس شبكات", "🟡 إجازة", "📁");
            AddEmployeeRow("👤", "1010", "مريم سالم أحمد", "خدمة العملاء", "مسؤول خدمة عملاء", "🟢 نشط", "📁");

            // إضافة معالج النقر على الجدول
            grid.CellClick += Grid_CellClick;

            // إنشاء panel للجدول مع التخطيط الذكي
            var gridPanel = new Controls.DoubleBufferedPanel
            {
                Dock = DockStyle.Fill,
                BackColor = Theme.ThemeManager.Colors.Background,
                Padding = new Padding(Theme.SmartLayoutManager.Spacing.ContainerMargin)
            };

            gridPanel.Controls.Add(grid);

            // تطبيق التخطيط الذكي للجدول
            Theme.SmartLayoutManager.ApplyDataGridLayout(grid, gridPanel);

            containerPanel.Controls.Add(gridPanel);
            containerPanel.Controls.Add(toolbarPanel);
            Controls.Add(containerPanel);

            // تطبيق التخطيط الذكي للنافذة كاملة
            this.Load += EmployeeManagementForm_Load;
            this.Shown += EmployeeManagementForm_Shown;
            this.Resize += (s, e) =>
            {
                if (this.Visible && grid != null && grid.Parent != null)
                {
                    ApplySmartLayout();
                }
            };

            Theme.ThemeManager.ApplyTheme(this);
        }

        private void EmployeeManagementForm_Load(object sender, EventArgs e)
        {
            // Apply layout after form is loaded
            ApplySmartLayout();
        }

        private void EmployeeManagementForm_Shown(object sender, EventArgs e)
        {
            // Apply layout again after form is fully visible with a small delay
            var timer = new System.Windows.Forms.Timer();
            timer.Interval = 50; // 50ms delay
            timer.Tick += (s, args) =>
            {
                timer.Stop();
                ApplySmartLayout();
                ApplyToolbarLayout();
            };
            timer.Start();
        }

        private void ApplyToolbarLayout()
        {
            try
            {
                // إعادة تطبيق تخطيط شريط الأدوات
                if (toolbarPanel != null && toolbarPanel.Controls.Count > 0)
                {
                    foreach (Control control in toolbarPanel.Controls)
                    {
                        if (control is Controls.DoubleBufferedPanel panel && panel.Controls.Count > 0)
                        {
                            // تطبيق التخطيط الموزع للأزرار فقط
                            if (panel.Dock == DockStyle.Fill) // هذا هو panel الأزرار
                            {
                                Theme.SmartLayoutManager.ApplySmartLayout(panel, Theme.SmartLayoutManager.LayoutMode.Distributed);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Toolbar layout error: {ex.Message}");
            }
        }

        private void ApplySmartLayout()
        {
            try
            {
                if (grid != null && grid.Parent != null && grid.Parent.Width > 0)
                {
                    Theme.SmartLayoutManager.ApplyDataGridLayout(grid, grid.Parent);
                }
            }
            catch (Exception ex)
            {
                // Log error but don't crash the application
                System.Diagnostics.Debug.WriteLine($"Smart layout error: {ex.Message}");
            }
        }

        private void AddEmployeeRow(string photo, string id, string name, string department, string position, string status, string fileIcon)
        {
            var rowIndex = grid.Rows.Add(photo, id, name, department, position, status, fileIcon);

            // تخصيص لون الحالة
            var statusCell = grid.Rows[rowIndex].Cells["Status"];
            if (status.Contains("نشط"))
            {
                statusCell.Style.ForeColor = Color.LightGreen;
            }
            else if (status.Contains("إجازة"))
            {
                statusCell.Style.ForeColor = Color.Orange;
            }
            else if (status.Contains("غائب"))
            {
                statusCell.Style.ForeColor = Color.LightCoral;
            }

            // تخصيص أيقونة الملف
            var fileCell = grid.Rows[rowIndex].Cells["Actions"];
            fileCell.Style.ForeColor = Color.FromArgb(255, 193, 7); // لون ذهبي للملف
            fileCell.ToolTipText = "انقر لفتح ملف الموظف";
        }

        private void Grid_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                // التحقق من النقر على عمود أيقونة الملف أو أي مكان في الصف
                if (grid.Columns[e.ColumnIndex].Name == "Actions" || e.ColumnIndex >= 0)
                {
                    var employeeId = grid.Rows[e.RowIndex].Cells["Id"].Value?.ToString();
                    var employeeName = grid.Rows[e.RowIndex].Cells["Name"].Value?.ToString();

                    if (!string.IsNullOrEmpty(employeeId))
                    {
                        // فتح نموذج ملف الموظف
                        var employeeFileForm = new EmployeeFileForm(employeeId, employeeName ?? "");
                        employeeFileForm.ShowDialog();
                    }
                }
            }
        }
    }
}
