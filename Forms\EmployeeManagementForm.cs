using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class EmployeeManagementForm : Form
    {
        private Guna2DataGridView grid;
        private Guna2Button addButton;
        private Panel toolbarPanel;

        public EmployeeManagementForm()
        {
            InitializeComponent();
            InitializeEmployees();
            Theme.ThemeManager.ThemeChanged += (s, e) => Theme.ThemeManager.ApplyTheme(this);
        }

        private void InitializeEmployees()
        {
            var containerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // إنشاء شريط الأدوات العلوي
            toolbarPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                Padding = new Padding(0, 0, 0, 20),
                BackColor = Color.FromArgb(40, 39, 84)
            };

            // إنشاء مجموعة الأزرار
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 600,
                Height = 60
            };

            // زر إضافة موظف
            addButton = new Guna2Button
            {
                Text = "إضافة موظف",
                Size = new Size(120, 40),
                Location = new Point(480, 10),
                FillColor = Color.FromArgb(40, 167, 69),
                HoverState = { FillColor = Color.FromArgb(34, 139, 58) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر تعديل موظف
            var editButton = new Guna2Button
            {
                Text = "تعديل",
                Size = new Size(100, 40),
                Location = new Point(370, 10),
                FillColor = Color.FromArgb(0, 123, 255),
                HoverState = { FillColor = Color.FromArgb(0, 105, 217) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر حذف موظف
            var deleteButton = new Guna2Button
            {
                Text = "حذف",
                Size = new Size(100, 40),
                Location = new Point(260, 10),
                FillColor = Color.FromArgb(220, 53, 69),
                HoverState = { FillColor = Color.FromArgb(200, 35, 51) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر تصدير البيانات
            var exportButton = new Guna2Button
            {
                Text = "تصدير Excel",
                Size = new Size(120, 40),
                Location = new Point(130, 10),
                FillColor = Color.FromArgb(108, 117, 125),
                HoverState = { FillColor = Color.FromArgb(90, 98, 104) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر طباعة
            var printButton = new Guna2Button
            {
                Text = "طباعة",
                Size = new Size(100, 40),
                Location = new Point(20, 10),
                FillColor = Color.FromArgb(102, 16, 242),
                HoverState = { FillColor = Color.FromArgb(85, 13, 202) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // مربع البحث
            var searchBox = new Guna2TextBox
            {
                PlaceholderText = "البحث عن موظف...",
                Size = new Size(250, 40),
                Location = new Point(20, 10),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White,
                PlaceholderForeColor = Color.LightGray,
                BorderColor = Color.FromArgb(70, 69, 114)
            };

            var searchPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 280
            };
            searchPanel.Controls.Add(searchBox);

            buttonsPanel.Controls.AddRange(new Control[] {
                addButton, editButton, deleteButton, exportButton, printButton
            });

            toolbarPanel.Controls.Add(buttonsPanel);
            toolbarPanel.Controls.Add(searchPanel);

            grid = new Guna2DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.FromArgb(34, 33, 74),
                ForeColor = Color.White,
                ThemeStyle = {
                    HeaderStyle = {
                        Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                        BackColor = Color.FromArgb(40, 39, 84),
                        ForeColor = Color.White,
                        Height = 50
                    },
                    RowsStyle = {
                        BackColor = Color.FromArgb(34, 33, 74),
                        Font = new Font("Segoe UI", 11F),
                        ForeColor = Color.White,
                        Height = 65
                    },
                    AlternatingRowsStyle = {
                        BackColor = Color.FromArgb(31, 30, 68),
                        Font = new Font("Segoe UI", 11F),
                        ForeColor = Color.White
                    }
                },
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                GridColor = Color.FromArgb(50, 49, 90),
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                DefaultCellStyle = {
                    SelectionBackColor = Color.FromArgb(0, 126, 249),
                    SelectionForeColor = Color.White,
                    Padding = new Padding(10, 5, 10, 5),
                    WrapMode = DataGridViewTriState.False
                },
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None,
                AllowUserToResizeColumns = true,
                AllowUserToResizeRows = false,
                RightToLeft = RightToLeft.Yes,
                ColumnHeadersHeight = 50,
                RowTemplate = { Height = 65 },
                ScrollBars = ScrollBars.Both,
                MultiSelect = false
            };

            // إضافة الأعمدة مع تخصيص مفصل
            var photoColumn = new DataGridViewTextBoxColumn
            {
                Name = "Photo",
                HeaderText = "الصورة",
                Width = 80,
                MinimumWidth = 80,
                Resizable = DataGridViewTriState.False,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 20F),
                    Padding = new Padding(5)
                }
            };

            var idColumn = new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "الرقم الوظيفي",
                Width = 130,
                MinimumWidth = 100,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                    ForeColor = Color.LightBlue
                }
            };

            var nameColumn = new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "اسم الموظف",
                Width = 220,
                MinimumWidth = 180,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                    Padding = new Padding(15, 5, 15, 5)
                }
            };

            var departmentColumn = new DataGridViewTextBoxColumn
            {
                Name = "Department",
                HeaderText = "القسم",
                Width = 200,
                MinimumWidth = 150,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11F),
                    ForeColor = Color.LightGreen,
                    Padding = new Padding(10, 5, 10, 5)
                }
            };

            var positionColumn = new DataGridViewTextBoxColumn
            {
                Name = "Position",
                HeaderText = "المنصب",
                Width = 220,
                MinimumWidth = 180,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11F),
                    ForeColor = Color.LightYellow,
                    Padding = new Padding(10, 5, 10, 5)
                }
            };

            var statusColumn = new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                Width = 130,
                MinimumWidth = 100,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                    Padding = new Padding(5)
                }
            };

            var actionsColumn = new DataGridViewButtonColumn
            {
                Name = "Actions",
                HeaderText = "الإجراءات",
                Width = 150,
                MinimumWidth = 120,
                Text = "عرض البروفايل",
                UseColumnTextForButtonValue = true,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    ForeColor = Color.White,
                    BackColor = Color.FromArgb(0, 126, 249),
                    SelectionBackColor = Color.FromArgb(0, 105, 217),
                    Padding = new Padding(5)
                }
            };

            // إضافة الأعمدة للجدول
            grid.Columns.AddRange(new DataGridViewColumn[] {
                photoColumn, idColumn, nameColumn, departmentColumn,
                positionColumn, statusColumn, actionsColumn
            });

            // تخصيص عام لرؤوس الأعمدة
            foreach (DataGridViewColumn column in grid.Columns)
            {
                column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.HeaderCell.Style.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                column.HeaderCell.Style.ForeColor = Color.White;
                column.HeaderCell.Style.BackColor = Color.FromArgb(40, 39, 84);
                column.HeaderCell.Style.Padding = new Padding(5);
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
            }

            // إضافة البيانات التجريبية المحسنة
            AddEmployeeRow("👤", "1001", "أحمد محمد علي", "تقنية المعلومات", "مطور برمجيات أول", "🟢 نشط");
            AddEmployeeRow("👤", "1002", "سارة أحمد خالد", "الموارد البشرية", "مدير موارد بشرية", "🟢 نشط");
            AddEmployeeRow("👤", "1003", "محمد علي حسن", "المالية والمحاسبة", "محاسب رئيسي", "🟡 إجازة");
            AddEmployeeRow("👤", "1004", "فاطمة خالد عمر", "إدارة العمليات", "مدير مشروع", "🟢 نشط");
            AddEmployeeRow("👤", "1005", "عمر يوسف سالم", "التسويق والمبيعات", "مسؤول تسويق رقمي", "🟢 نشط");
            AddEmployeeRow("👤", "1006", "نورا حسام الدين", "تقنية المعلومات", "مصمم واجهات مستخدم", "🟢 نشط");
            AddEmployeeRow("👤", "1007", "خالد عبدالله محمد", "المبيعات", "مندوب مبيعات أول", "🟢 نشط");
            AddEmployeeRow("👤", "1008", "ريم محمد أحمد", "الموارد البشرية", "أخصائي توظيف", "🟢 نشط");
            AddEmployeeRow("👤", "1009", "يوسف علي حسن", "تقنية المعلومات", "مهندس شبكات", "🟡 إجازة");
            AddEmployeeRow("👤", "1010", "مريم سالم أحمد", "خدمة العملاء", "مسؤول خدمة عملاء", "🟢 نشط");

            // إضافة معالج النقر على الجدول
            grid.CellClick += Grid_CellClick;

            containerPanel.Controls.Add(grid);
            containerPanel.Controls.Add(toolbarPanel);
            Controls.Add(containerPanel);
            Theme.ThemeManager.ApplyTheme(this);
        }

        private void AddEmployeeRow(string photo, string id, string name, string department, string position, string status)
        {
            var rowIndex = grid.Rows.Add(photo, id, name, department, position, status, "");

            // تخصيص لون الحالة
            var statusCell = grid.Rows[rowIndex].Cells["Status"];
            if (status.Contains("نشط"))
            {
                statusCell.Style.ForeColor = Color.LightGreen;
            }
            else if (status.Contains("إجازة"))
            {
                statusCell.Style.ForeColor = Color.Orange;
            }
            else if (status.Contains("غائب"))
            {
                statusCell.Style.ForeColor = Color.LightCoral;
            }
        }

        private void Grid_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var employeeId = grid.Rows[e.RowIndex].Cells["Id"].Value?.ToString();
                var employeeName = grid.Rows[e.RowIndex].Cells["Name"].Value?.ToString();

                if (!string.IsNullOrEmpty(employeeId))
                {
                    // فتح نموذج بروفايل الموظف
                    var profileForm = new EmployeeProfileForm(employeeId, employeeName ?? "");
                    profileForm.ShowDialog();
                }
            }
        }
    }
}
