using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class EmployeeManagementForm : Form
    {
        private Guna2DataGridView grid;
        private Guna2Button addButton;
        private Panel toolbarPanel;

        public EmployeeManagementForm()
        {
            InitializeComponent();
            InitializeEmployees();
            Theme.ThemeManager.ThemeChanged += (s, e) => Theme.ThemeManager.ApplyTheme(this);
        }

        private void InitializeEmployees()
        {
            var containerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // إنشاء شريط الأدوات العلوي
            toolbarPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                Padding = new Padding(0, 0, 0, 20),
                BackColor = Color.FromArgb(40, 39, 84)
            };

            // إنشاء مجموعة الأزرار
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 600,
                Height = 60
            };

            // زر إضافة موظف
            addButton = new Guna2Button
            {
                Text = "إضافة موظف",
                Size = new Size(120, 40),
                Location = new Point(480, 10),
                FillColor = Color.FromArgb(40, 167, 69),
                HoverState = { FillColor = Color.FromArgb(34, 139, 58) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر تعديل موظف
            var editButton = new Guna2Button
            {
                Text = "تعديل",
                Size = new Size(100, 40),
                Location = new Point(370, 10),
                FillColor = Color.FromArgb(0, 123, 255),
                HoverState = { FillColor = Color.FromArgb(0, 105, 217) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر حذف موظف
            var deleteButton = new Guna2Button
            {
                Text = "حذف",
                Size = new Size(100, 40),
                Location = new Point(260, 10),
                FillColor = Color.FromArgb(220, 53, 69),
                HoverState = { FillColor = Color.FromArgb(200, 35, 51) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر تصدير البيانات
            var exportButton = new Guna2Button
            {
                Text = "تصدير Excel",
                Size = new Size(120, 40),
                Location = new Point(130, 10),
                FillColor = Color.FromArgb(108, 117, 125),
                HoverState = { FillColor = Color.FromArgb(90, 98, 104) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // زر طباعة
            var printButton = new Guna2Button
            {
                Text = "طباعة",
                Size = new Size(100, 40),
                Location = new Point(20, 10),
                FillColor = Color.FromArgb(102, 16, 242),
                HoverState = { FillColor = Color.FromArgb(85, 13, 202) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };

            // مربع البحث
            var searchBox = new Guna2TextBox
            {
                PlaceholderText = "البحث عن موظف...",
                Size = new Size(250, 40),
                Location = new Point(20, 10),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White,
                PlaceholderForeColor = Color.LightGray,
                BorderColor = Color.FromArgb(70, 69, 114)
            };

            var searchPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 280
            };
            searchPanel.Controls.Add(searchBox);

            buttonsPanel.Controls.AddRange(new Control[] {
                addButton, editButton, deleteButton, exportButton, printButton
            });

            toolbarPanel.Controls.Add(buttonsPanel);
            toolbarPanel.Controls.Add(searchPanel);

            grid = new Guna2DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Theme.ThemeManager.Colors.Secondary,
                ForeColor = Theme.ThemeManager.Colors.Text,
                ThemeStyle = {
                    HeaderStyle = {
                        Font = new Font(Theme.ThemeManager.Fonts.Subtitle.FontFamily, 11, FontStyle.Bold),
                        BackColor = Theme.ThemeManager.Effects.DataGrid.HeaderBackground,
                        ForeColor = Theme.ThemeManager.Colors.Text
                    },
                    RowsStyle = {
                        BackColor = Theme.ThemeManager.Effects.DataGrid.RowBackground,
                        Font = new Font(Theme.ThemeManager.Fonts.Regular.FontFamily, 10),
                        ForeColor = Theme.ThemeManager.Colors.Text
                    },
                    AlternatingRowsStyle = {
                        BackColor = Theme.ThemeManager.Effects.DataGrid.AlternateRowBackground,
                        Font = new Font(Theme.ThemeManager.Fonts.Regular.FontFamily, 10),
                        ForeColor = Theme.ThemeManager.Colors.Text
                    }
                },
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                GridColor = Theme.ThemeManager.Colors.Border,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                DefaultCellStyle = {
                    SelectionBackColor = Theme.ThemeManager.Effects.DataGrid.SelectionBackground,
                    SelectionForeColor = Theme.ThemeManager.Colors.Text,
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RightToLeft = RightToLeft.Yes,
                ColumnHeadersHeight = Theme.ThemeManager.Dimensions.GridHeaderHeight,
                RowTemplate = { Height = Theme.ThemeManager.Dimensions.GridRowHeight }
            };

            // إضافة الأعمدة
            grid.Columns.Add("Id", "الرقم الوظيفي");
            grid.Columns.Add("Name", "اسم الموظف");
            grid.Columns.Add("Department", "القسم");
            grid.Columns.Add("Position", "المنصب");
            grid.Columns.Add("Phone", "رقم الهاتف");
            grid.Columns.Add("Email", "البريد الإلكتروني");
            grid.Columns.Add("JoinDate", "تاريخ التعيين");
            grid.Columns.Add("Salary", "الراتب");
            grid.Columns.Add("Status", "الحالة");
            grid.Columns.Add("Actions", "الإجراءات");

            // تخصيص عرض الأعمدة
            grid.Columns["Id"].Width = 80;
            grid.Columns["Name"].Width = 150;
            grid.Columns["Department"].Width = 120;
            grid.Columns["Position"].Width = 130;
            grid.Columns["Phone"].Width = 120;
            grid.Columns["Email"].Width = 180;
            grid.Columns["JoinDate"].Width = 100;
            grid.Columns["Salary"].Width = 100;
            grid.Columns["Status"].Width = 100;
            grid.Columns["Actions"].Width = 120;

            // إضافة البيانات التجريبية
            grid.Rows.Add("1001", "أحمد محمد علي", "تقنية المعلومات", "مطور برمجيات", "0501234567", "<EMAIL>", "2025-01-15", "12,000 ريال", "نشط", "عرض");
            grid.Rows.Add("1002", "سارة أحمد خالد", "الموارد البشرية", "مدير موارد بشرية", "0507654321", "<EMAIL>", "2024-11-01", "15,000 ريال", "نشط", "عرض");
            grid.Rows.Add("1003", "محمد علي حسن", "المالية", "محاسب", "0509876543", "<EMAIL>", "2025-03-20", "10,000 ريال", "إجازة", "عرض");
            grid.Rows.Add("1004", "فاطمة خالد عمر", "العمليات", "مدير مشروع", "0502468135", "<EMAIL>", "2025-02-01", "14,000 ريال", "نشط", "عرض");
            grid.Rows.Add("1005", "عمر يوسف سالم", "التسويق", "مسؤول تسويق", "0508642097", "<EMAIL>", "2025-04-10", "11,000 ريال", "نشط", "عرض");
            grid.Rows.Add("1006", "نورا حسام الدين", "تقنية المعلومات", "مصمم واجهات", "0503691472", "<EMAIL>", "2025-05-01", "9,500 ريال", "نشط", "عرض");
            grid.Rows.Add("1007", "خالد عبدالله", "المبيعات", "مندوب مبيعات", "0507418529", "<EMAIL>", "2025-03-15", "8,000 ريال", "نشط", "عرض");
            grid.Rows.Add("1008", "ريم محمد", "الموارد البشرية", "أخصائي توظيف", "0509517428", "<EMAIL>", "2025-04-20", "9,000 ريال", "نشط", "عرض");

            foreach (DataGridViewColumn column in grid.Columns)
            {
                column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
            }

            containerPanel.Controls.Add(grid);
            containerPanel.Controls.Add(toolbarPanel);
            Controls.Add(containerPanel);
            Theme.ThemeManager.ApplyTheme(this);
        }
    }
}
