using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class EmployeeManagementForm : Form
    {
        private Guna2DataGridView grid;
        private Guna2Button addButton;
        private Panel toolbarPanel;

        public EmployeeManagementForm()
        {
            InitializeComponent();
            InitializeEmployees();
            Theme.ThemeManager.ThemeChanged += (s, e) => Theme.ThemeManager.ApplyTheme(this);
        }

        private void InitializeEmployees()
        {
            var containerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(Theme.ThemeManager.Dimensions.Padding)
            };

            toolbarPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 70,
                Padding = new Padding(0, 0, 0, 20)
            };

            addButton = Theme.ThemeManager.CreateFluentButton();
            addButton.Text = "إضافة موظف";
            addButton.Size = new Size(150, 45);
            addButton.Image = null;
            addButton.ImageAlign = HorizontalAlignment.Left;
            addButton.TextAlign = HorizontalAlignment.Center;
            addButton.Dock = DockStyle.Right;
            addButton.FillColor = Theme.ThemeManager.Colors.Success;
            addButton.HoverState.FillColor = Color.FromArgb(200, Theme.ThemeManager.Colors.Success);

            toolbarPanel.Controls.Add(addButton);

            grid = new Guna2DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Theme.ThemeManager.Colors.Secondary,
                ForeColor = Theme.ThemeManager.Colors.Text,
                ThemeStyle = {
                    HeaderStyle = { 
                        Font = new Font(Theme.ThemeManager.Fonts.Subtitle.FontFamily, 11, FontStyle.Bold),
                        BackColor = Theme.ThemeManager.Effects.DataGrid.HeaderBackground,
                        ForeColor = Theme.ThemeManager.Colors.Text
                    },
                    RowsStyle = { 
                        BackColor = Theme.ThemeManager.Effects.DataGrid.RowBackground,
                        Font = new Font(Theme.ThemeManager.Fonts.Regular.FontFamily, 10),
                        ForeColor = Theme.ThemeManager.Colors.Text
                    },
                    AlternatingRowsStyle = {
                        BackColor = Theme.ThemeManager.Effects.DataGrid.AlternateRowBackground,
                        Font = new Font(Theme.ThemeManager.Fonts.Regular.FontFamily, 10),
                        ForeColor = Theme.ThemeManager.Colors.Text
                    }
                },
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                GridColor = Theme.ThemeManager.Colors.Border,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                DefaultCellStyle = { 
                    SelectionBackColor = Theme.ThemeManager.Effects.DataGrid.SelectionBackground,
                    SelectionForeColor = Theme.ThemeManager.Colors.Text,
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RightToLeft = RightToLeft.Yes,
                ColumnHeadersHeight = Theme.ThemeManager.Dimensions.GridHeaderHeight,
                RowTemplate = { Height = Theme.ThemeManager.Dimensions.GridRowHeight }
            };

            grid.Columns.Add("Id", "الرقم الوظيفي");
            grid.Columns.Add("Name", "اسم الموظف");
            grid.Columns.Add("Department", "القسم");
            grid.Columns.Add("Position", "المنصب");
            grid.Columns.Add("JoinDate", "تاريخ التعيين");
            grid.Columns.Add("Status", "الحالة");

            grid.Rows.Add("1001", "أحمد محمد", "تقنية المعلومات", "مطور برمجيات", "2025-01-15", "دوام كامل");
            grid.Rows.Add("1002", "سارة أحمد", "الموارد البشرية", "مدير موارد بشرية", "2024-11-01", "دوام كامل");
            grid.Rows.Add("1003", "محمد علي", "المالية", "محاسب", "2025-03-20", "دوام جزئي");
            grid.Rows.Add("1004", "فاطمة خالد", "العمليات", "مدير مشروع", "2025-02-01", "دوام كامل");
            grid.Rows.Add("1005", "عمر يوسف", "التسويق", "مسؤول تسويق", "2025-04-10", "دوام كامل");

            foreach (DataGridViewColumn column in grid.Columns)
            {
                column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
            }

            containerPanel.Controls.Add(grid);
            containerPanel.Controls.Add(toolbarPanel);
            Controls.Add(containerPanel);
            Theme.ThemeManager.ApplyTheme(this);
        }
    }
}
