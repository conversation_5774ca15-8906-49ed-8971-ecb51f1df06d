using System.Linq;
using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class HRDashboardForm : Form
    {
        public HRDashboardForm()
        {
            InitializeComponent();
            InitializeDashboard();
        }

        private void InitializeDashboard()
        {
            this.BackColor = Theme.ThemeManager.Colors.Background;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Apply responsive design
            Theme.ResponsiveManager.ApplyResponsiveDesign(this);

            CreateModernDashboard();

            // Apply smart layout after form is fully loaded
            this.Load += HRDashboardForm_Load;
            this.Shown += HRDashboardForm_Shown;
        }

        private void HRDashboardForm_Load(object sender, EventArgs e)
        {
            // Initial layout application
            ApplySmartLayoutSafe();
        }

        private void HRDashboardForm_Shown(object sender, EventArgs e)
        {
            // Apply layout again after form is fully visible with a small delay
            var timer = new System.Windows.Forms.Timer();
            timer.Interval = 100; // 100ms delay
            timer.Tick += (s, args) =>
            {
                timer.Stop();
                ApplySmartLayoutSafe();
            };
            timer.Start();
        }

        private void ApplySmartLayoutSafe()
        {
            try
            {
                if (this.Controls.Count > 0)
                {
                    var cardsContainer = this.Controls.OfType<Controls.DoubleBufferedPanel>().FirstOrDefault();
                    if (cardsContainer != null && cardsContainer.Width > 0 && cardsContainer.Height > 0)
                    {
                        Theme.SmartLayoutManager.ApplySmartLayout(cardsContainer, Theme.SmartLayoutManager.LayoutMode.Adaptive);
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't crash the application
                System.Diagnostics.Debug.WriteLine($"Smart layout error: {ex.Message}");
            }
        }

        private Size CalculateOptimalCardSize()
        {
            // حساب الحجم الأمثل للبطاقات بناءً على حجم الشاشة
            var baseWidth = (int)(320 * Theme.ResponsiveManager.ScaleFactor);
            var baseHeight = (int)(200 * Theme.ResponsiveManager.ScaleFactor);

            return new Size(baseWidth, baseHeight);
        }

        private Panel CreateStatisticsCard(string title, string value, string unit, string icon, Color accentColor, Size cardSize)
        {
            var card = new Controls.DoubleBufferedPanel
            {
                Size = cardSize,
                BackColor = Theme.ThemeManager.Colors.Card,
                BorderStyle = BorderStyle.None,
                Padding = new Padding(20),
                RightToLeft = RightToLeft.Yes
            };

            // Add shadow effect
            card.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, card.Width, card.Height);
                using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(shadowBrush, rect.X + 3, rect.Y + 3, rect.Width, rect.Height);
                }
                using (var cardBrush = new SolidBrush(card.BackColor))
                {
                    e.Graphics.FillRectangle(cardBrush, rect);
                }
                using (var borderPen = new Pen(Color.FromArgb(30, accentColor), 2))
                {
                    e.Graphics.DrawRectangle(borderPen, rect);
                }
            };

            // Title
            var titleLabel = new Label
            {
                Text = title,
                Font = Theme.ResponsiveManager.Fonts.Subtitle,
                ForeColor = Theme.ThemeManager.Colors.TextSecondary,
                Location = new Point(20, 20),
                Size = new Size(cardSize.Width - 40, 30),
                TextAlign = ContentAlignment.TopRight,
                RightToLeft = RightToLeft.Yes
            };

            // Value
            var valueLabel = new Label
            {
                Text = value,
                Font = Theme.ResponsiveManager.Fonts.ExtraLargeTitle,
                ForeColor = accentColor,
                Location = new Point(20, 60),
                Size = new Size(cardSize.Width - 120, 50),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // Unit
            var unitLabel = new Label
            {
                Text = unit,
                Font = Theme.ResponsiveManager.Fonts.Regular,
                ForeColor = Theme.ThemeManager.Colors.TextMuted,
                Location = new Point(20, 115),
                Size = new Size(cardSize.Width - 120, 25),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // Icon
            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", Theme.ResponsiveManager.ScaleFactor * 32),
                ForeColor = accentColor,
                Location = new Point(cardSize.Width - 80, 60),
                Size = new Size(60, 60),
                TextAlign = ContentAlignment.MiddleCenter
            };

            card.Controls.AddRange(new Control[] { titleLabel, valueLabel, unitLabel, iconLabel });
            return card;
        }

        private Panel CreateProgressCard(string title, int percentage, Color progressColor, Size cardSize)
        {
            var card = new Controls.DoubleBufferedPanel
            {
                Size = cardSize,
                BackColor = Theme.ThemeManager.Colors.Card,
                BorderStyle = BorderStyle.None,
                Padding = new Padding(20),
                RightToLeft = RightToLeft.Yes
            };

            // Add shadow effect
            card.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, card.Width, card.Height);
                using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(shadowBrush, rect.X + 3, rect.Y + 3, rect.Width, rect.Height);
                }
                using (var cardBrush = new SolidBrush(card.BackColor))
                {
                    e.Graphics.FillRectangle(cardBrush, rect);
                }
                using (var borderPen = new Pen(Color.FromArgb(30, progressColor), 2))
                {
                    e.Graphics.DrawRectangle(borderPen, rect);
                }
            };

            // Title
            var titleLabel = new Label
            {
                Text = title,
                Font = Theme.ResponsiveManager.Fonts.Subtitle,
                ForeColor = Theme.ThemeManager.Colors.TextSecondary,
                Location = new Point(20, 20),
                Size = new Size(cardSize.Width - 40, 30),
                TextAlign = ContentAlignment.TopRight,
                RightToLeft = RightToLeft.Yes
            };

            // Percentage
            var percentageLabel = new Label
            {
                Text = $"{percentage}%",
                Font = Theme.ResponsiveManager.Fonts.LargeTitle,
                ForeColor = progressColor,
                Location = new Point(20, 60),
                Size = new Size(cardSize.Width - 40, 40),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            // Progress Bar
            var progressBar = Theme.ModernUIComponents.CreateModernProgressBar(percentage, progressColor);
            progressBar.Location = new Point(30, 120);
            progressBar.Size = new Size(cardSize.Width - 60, 25);

            card.Controls.AddRange(new Control[] { titleLabel, percentageLabel, progressBar });
            return card;
        }

        private Panel CreateInfoCard(string title, string content, Size cardSize)
        {
            var card = new Controls.DoubleBufferedPanel
            {
                Size = cardSize,
                BackColor = Theme.ThemeManager.Colors.Card,
                BorderStyle = BorderStyle.None,
                Padding = new Padding(20),
                RightToLeft = RightToLeft.Yes
            };

            // Add shadow effect
            card.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, card.Width, card.Height);
                using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(shadowBrush, rect.X + 3, rect.Y + 3, rect.Width, rect.Height);
                }
                using (var cardBrush = new SolidBrush(card.BackColor))
                {
                    e.Graphics.FillRectangle(cardBrush, rect);
                }
                using (var borderPen = new Pen(Color.FromArgb(30, Theme.ThemeManager.Colors.Info), 2))
                {
                    e.Graphics.DrawRectangle(borderPen, rect);
                }
            };

            // Title
            var titleLabel = new Label
            {
                Text = title,
                Font = Theme.ResponsiveManager.Fonts.Subtitle,
                ForeColor = Theme.ThemeManager.Colors.TextSecondary,
                Location = new Point(20, 20),
                Size = new Size(cardSize.Width - 40, 30),
                TextAlign = ContentAlignment.TopRight,
                RightToLeft = RightToLeft.Yes
            };

            // Content
            var contentLabel = new Label
            {
                Text = content,
                Font = Theme.ResponsiveManager.Fonts.Regular,
                ForeColor = Theme.ThemeManager.Colors.Text,
                Location = new Point(20, 60),
                Size = new Size(cardSize.Width - 40, cardSize.Height - 100),
                TextAlign = ContentAlignment.TopRight,
                RightToLeft = RightToLeft.Yes
            };

            card.Controls.AddRange(new Control[] { titleLabel, contentLabel });
            return card;
        }

        private void CreateModernDashboard()
        {
            // إنشاء container للبطاقات مع تحسينات التخطيط
            var cardsContainer = new Controls.DoubleBufferedPanel
            {
                Dock = DockStyle.Fill,
                BackColor = Theme.ThemeManager.Colors.Background,
                Padding = new Padding(Theme.SmartLayoutManager.Spacing.ContainerMargin),
                AutoScroll = true
            };

            // تحسين أبعاد البطاقات للتخطيط الاحترافي
            var cardSize = CalculateOptimalCardSize();

            // إجمالي الموظفين - بطاقة إحصائية
            var employeesCard = CreateStatisticsCard("إجمالي الموظفين", "25", "موظف",
                "👥", Theme.ThemeManager.Colors.Accent, cardSize);

            // معدل الحضور - بطاقة تقدم
            var attendanceCard = CreateProgressCard("معدل الحضور اليوم", 92,
                Theme.ThemeManager.Colors.Success, cardSize);

            // إجمالي الرواتب الشهرية - بطاقة إحصائية
            var salariesCard = CreateStatisticsCard("إجمالي الرواتب الشهرية", "125,000", "ريال",
                "💰", Theme.ThemeManager.Colors.Warning, cardSize);

            // نسبة الحضور الشهري - بطاقة تقدم دائري
            var chartCard = CreateProgressCard("نسبة الحضور الشهري", 75,
                Theme.ThemeManager.Colors.Info, cardSize);

            // توزيع الموظفين حسب الأقسام - بطاقة معلومات
            var departmentsCard = CreateInfoCard("توزيع الموظفين حسب الأقسام",
                "• تقنية المعلومات: 8 موظفين\n• الموارد البشرية: 4 موظفين\n• المالية: 6 موظفين\n• العمليات: 4 موظفين\n• التسويق: 3 موظفين",
                cardSize);

            // إحصائيات سريعة - بطاقة معلومات
            var quickStatsCard = CreateInfoCard("إحصائيات سريعة",
                "• موظفين جدد هذا الشهر: 2\n• إجازات معلقة: 3\n• تقييمات مستحقة: 5\n• عقود تنتهي قريباً: 1",
                cardSize);

            // موظفين نشطين - بطاقة إحصائية
            var activeEmployeesCard = CreateStatisticsCard("الموظفين النشطين", "23", "نشط",
                "✅", Theme.ThemeManager.Colors.Success, cardSize);

            // متوسط الراتب - بطاقة إحصائية
            var avgSalaryCard = CreateStatisticsCard("متوسط الراتب", "5,000", "ريال",
                "📊", Theme.ThemeManager.Colors.Info, cardSize);

            // زر الإشعار التجريبي
            var notificationButton = Theme.ModernUIComponents.CreateModernButton("عرض إشعار تجريبي",
                Theme.ModernUIComponents.ModernButtonStyle.Info);
            notificationButton.Size = new Size((int)(200 * Theme.ResponsiveManager.ScaleFactor),
                                              Theme.ResponsiveManager.Dimensions.ButtonHeight);
            notificationButton.Click += (s, e) =>
            {
                Theme.ModernUIComponents.ShowModernNotification("مرحباً! هذا إشعار تجريبي بالتصميم العصري الجديد",
                    Theme.ModernUIComponents.NotificationType.Success);
            };

            // إضافة جميع البطاقات للحاوي
            cardsContainer.Controls.AddRange(new Control[] {
                employeesCard, attendanceCard, salariesCard, chartCard,
                departmentsCard, quickStatsCard, notificationButton
            });

            // إضافة الحاوي للنافذة
            this.Controls.Add(cardsContainer);

            // تطبيق التخطيط الذكي عند تغيير حجم النافذة (بعد التحميل)
            this.Resize += (s, e) =>
            {
                if (this.Visible && cardsContainer.Width > 0)
                {
                    ApplySmartLayoutSafe();
                }
            };
        }
    }
}
