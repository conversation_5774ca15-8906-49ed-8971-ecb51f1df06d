using System.Linq;
using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class HRDashboardForm : Form
    {
        public HRDashboardForm()
        {
            InitializeComponent();
            InitializeDashboard();
        }

        private void InitializeDashboard()
        {
            this.BackColor = Theme.ThemeManager.Colors.Background;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Apply responsive design
            Theme.ResponsiveManager.ApplyResponsiveDesign(this);

            CreateModernDashboard();

            // Apply smart layout after form is fully loaded
            this.Load += HRDashboardForm_Load;
            this.Shown += HRDashboardForm_Shown;
        }

        private void HRDashboardForm_Load(object sender, EventArgs e)
        {
            // Initial layout application
            ApplySmartLayoutSafe();
        }

        private void HRDashboardForm_Shown(object sender, EventArgs e)
        {
            // Apply layout again after form is fully visible
            ApplySmartLayoutSafe();
        }

        private void ApplySmartLayoutSafe()
        {
            try
            {
                if (this.Controls.Count > 0)
                {
                    var cardsContainer = this.Controls.OfType<Controls.DoubleBufferedPanel>().FirstOrDefault();
                    if (cardsContainer != null && cardsContainer.Width > 0 && cardsContainer.Height > 0)
                    {
                        Theme.SmartLayoutManager.ApplySmartLayout(cardsContainer, Theme.SmartLayoutManager.LayoutMode.Adaptive);
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't crash the application
                System.Diagnostics.Debug.WriteLine($"Smart layout error: {ex.Message}");
            }
        }

        private void CreateModernDashboard()
        {
            // إنشاء container للبطاقات
            var cardsContainer = new Controls.DoubleBufferedPanel
            {
                Dock = DockStyle.Fill,
                BackColor = Theme.ThemeManager.Colors.Background,
                Padding = new Padding(Theme.SmartLayoutManager.Spacing.ContainerMargin)
            };

            // إجمالي الموظفين
            var employeesCard = Theme.ModernUIComponents.CreateModernCard("إجمالي الموظفين",
                new Size(Theme.ResponsiveManager.Dimensions.CardWidth, Theme.ResponsiveManager.Dimensions.CardHeight));

            var lblEmployees = new Label
            {
                Text = "25 موظف",
                ForeColor = Theme.ThemeManager.Colors.Accent,
                Font = Theme.ResponsiveManager.Fonts.LargeTitle,
                Location = new Point(50, 80),
                Size = new Size(200, 50),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            var employeesIcon = new Label
            {
                Text = "👥",
                Font = new Font("Segoe UI Emoji", Theme.ResponsiveManager.ScaleFactor * 24),
                ForeColor = Theme.ThemeManager.Colors.Accent,
                Location = new Point(50, 120),
                Size = new Size(200, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            employeesCard.Controls.AddRange(new Control[] { lblEmployees, employeesIcon });

            // معدل الحضور
            var attendanceCard = Theme.ModernUIComponents.CreateModernCard("معدل الحضور اليوم",
                new Size(Theme.ResponsiveManager.Dimensions.CardWidth, Theme.ResponsiveManager.Dimensions.CardHeight));

            var lblAttendance = new Label
            {
                Text = "92%",
                ForeColor = Theme.ThemeManager.Colors.Success,
                Font = Theme.ResponsiveManager.Fonts.LargeTitle,
                Location = new Point(50, 80),
                Size = new Size(200, 50),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            var attendanceProgress = Theme.ModernUIComponents.CreateModernProgressBar(92, Theme.ThemeManager.Colors.Success);
            attendanceProgress.Location = new Point(50, 140);
            attendanceProgress.Size = new Size(200, 20);

            attendanceCard.Controls.AddRange(new Control[] { lblAttendance, attendanceProgress });

            // إجمالي الرواتب الشهرية
            var salariesCard = Theme.ModernUIComponents.CreateModernCard("إجمالي الرواتب الشهرية",
                new Size(Theme.ResponsiveManager.Dimensions.CardWidth, Theme.ResponsiveManager.Dimensions.CardHeight));

            var lblSalaries = new Label
            {
                Text = "125,000 ريال",
                ForeColor = Theme.ThemeManager.Colors.Warning,
                Font = Theme.ResponsiveManager.Fonts.Title,
                Location = new Point(30, 80),
                Size = new Size(240, 50),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            var salariesIcon = new Label
            {
                Text = "💰",
                Font = new Font("Segoe UI Emoji", Theme.ResponsiveManager.ScaleFactor * 24),
                ForeColor = Theme.ThemeManager.Colors.Warning,
                Location = new Point(50, 120),
                Size = new Size(200, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            salariesCard.Controls.AddRange(new Control[] { lblSalaries, salariesIcon });

            // مخطط دائري لتوزيع الأقسام
            var chartCard = Theme.ModernUIComponents.CreateModernCard("نسبة الحضور الشهري",
                new Size(Theme.ResponsiveManager.Dimensions.CardWidth, Theme.ResponsiveManager.Dimensions.CardHeight));

            var departmentChart = new Guna2CircleProgressBar
            {
                Value = 75,
                Size = new Size((int)(150 * Theme.ResponsiveManager.ScaleFactor), (int)(150 * Theme.ResponsiveManager.ScaleFactor)),
                Location = new Point(75, 60),
                FillColor = Theme.ThemeManager.Colors.Card,
                ProgressColor = Theme.ThemeManager.Colors.Accent,
                ProgressColor2 = Theme.ThemeManager.Colors.AccentLight,
                ForeColor = Theme.ThemeManager.Colors.Text,
                Font = Theme.ResponsiveManager.Fonts.Title
            };

            departmentChart.Text = "75%";
            chartCard.Controls.Add(departmentChart);

            // قائمة الأقسام
            var departmentsCard = Theme.ModernUIComponents.CreateModernCard("توزيع الموظفين حسب الأقسام",
                new Size(Theme.ResponsiveManager.Dimensions.CardWidth + 50, Theme.ResponsiveManager.Dimensions.CardHeight));

            var deptList = new Label
            {
                Text = "• تقنية المعلومات: 8 موظفين\n• الموارد البشرية: 4 موظفين\n• المالية: 6 موظفين\n• العمليات: 4 موظفين\n• التسويق: 3 موظفين",
                ForeColor = Theme.ThemeManager.Colors.TextSecondary,
                Font = Theme.ResponsiveManager.Fonts.Regular,
                Location = new Point(20, 60),
                Size = new Size(300, 120),
                RightToLeft = RightToLeft.Yes,
                TextAlign = ContentAlignment.TopRight
            };

            departmentsCard.Controls.Add(deptList);

            // إحصائيات سريعة
            var quickStatsCard = Theme.ModernUIComponents.CreateModernCard("إحصائيات سريعة",
                new Size(Theme.ResponsiveManager.Dimensions.CardWidth, Theme.ResponsiveManager.Dimensions.CardHeight));

            var statsText = new Label
            {
                Text = "• موظفين جدد هذا الشهر: 2\n• إجازات معلقة: 3\n• تقييمات مستحقة: 5\n• عقود تنتهي قريباً: 1",
                ForeColor = Theme.ThemeManager.Colors.TextSecondary,
                Font = Theme.ResponsiveManager.Fonts.Regular,
                Location = new Point(15, 60),
                Size = new Size(270, 120),
                RightToLeft = RightToLeft.Yes,
                TextAlign = ContentAlignment.TopRight
            };

            quickStatsCard.Controls.Add(statsText);

            // Add modern notification example
            var notificationButton = Theme.ModernUIComponents.CreateModernButton("عرض إشعار تجريبي",
                Theme.ModernUIComponents.ModernButtonStyle.Info);
            notificationButton.Click += (s, e) =>
            {
                Theme.ModernUIComponents.ShowModernNotification("مرحباً! هذا إشعار تجريبي بالتصميم العصري الجديد",
                    Theme.ModernUIComponents.NotificationType.Success);
            };

            // إضافة جميع البطاقات للحاوي
            cardsContainer.Controls.AddRange(new Control[] {
                employeesCard, attendanceCard, salariesCard, chartCard,
                departmentsCard, quickStatsCard, notificationButton
            });

            // إضافة الحاوي للنافذة
            this.Controls.Add(cardsContainer);

            // تطبيق التخطيط الذكي عند تغيير حجم النافذة (بعد التحميل)
            this.Resize += (s, e) =>
            {
                if (this.Visible && cardsContainer.Width > 0)
                {
                    ApplySmartLayoutSafe();
                }
            };
        }
    }
}
