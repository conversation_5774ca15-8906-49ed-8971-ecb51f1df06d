using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class HRDashboardForm : Form
    {
        public HRDashboardForm()
        {
            InitializeComponent();
            InitializeDashboard();
        }

        private void InitializeDashboard()
        {
            this.BackColor = Color.FromArgb(34, 33, 74);

            // إجمالي الموظفين
            var employeesCard = new Guna2GroupBox
            {
                Text = "إجمالي الموظفين",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(0, 126, 249),
                BorderColor = Color.FromArgb(0, 126, 249),
                Size = new Size(200, 150),
                Location = new Point(30, 30)
            };

            var lblEmployees = new Label
            {
                Text = "25 موظف",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(50, 70),
                AutoSize = true
            };

            employeesCard.Controls.Add(lblEmployees);
            this.Controls.Add(employeesCard);

            // معدل الحضور
            var attendanceCard = new Guna2GroupBox
            {
                Text = "معدل الحضور اليوم",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(40, 167, 69),
                BorderColor = Color.FromArgb(40, 167, 69),
                Size = new Size(200, 150),
                Location = new Point(260, 30)
            };

            var lblAttendance = new Label
            {
                Text = "92%",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(70, 70),
                AutoSize = true
            };

            attendanceCard.Controls.Add(lblAttendance);
            this.Controls.Add(attendanceCard);

            // إجمالي الرواتب الشهرية
            var salariesCard = new Guna2GroupBox
            {
                Text = "إجمالي الرواتب الشهرية",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(255, 193, 7),
                BorderColor = Color.FromArgb(255, 193, 7),
                Size = new Size(200, 150),
                Location = new Point(490, 30)
            };

            var lblSalaries = new Label
            {
                Text = "125,000 ريال",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(30, 70),
                AutoSize = true
            };

            salariesCard.Controls.Add(lblSalaries);
            this.Controls.Add(salariesCard);

            // مخطط دائري لتوزيع الأقسام
            var departmentChart = new Guna2CircleProgressBar
            {
                Value = 75,
                Size = new Size(150, 150),
                Location = new Point(30, 220),
                FillColor = Color.FromArgb(26, 25, 62),
                ProgressColor = Color.FromArgb(0, 126, 249),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold)
            };

            departmentChart.Text = "75%";
            this.Controls.Add(departmentChart);

            // تسمية للمخطط
            var chartLabel = new Label
            {
                Text = "نسبة الحضور الشهري",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                Location = new Point(30, 380),
                AutoSize = true
            };
            this.Controls.Add(chartLabel);

            // قائمة الأقسام
            var departmentsGroup = new Guna2GroupBox
            {
                Text = "توزيع الموظفين حسب الأقسام",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(108, 117, 125),
                BorderColor = Color.FromArgb(108, 117, 125),
                Size = new Size(300, 200),
                Location = new Point(220, 220)
            };

            var deptList = new Label
            {
                Text = "• تقنية المعلومات: 8 موظفين\n• الموارد البشرية: 4 موظفين\n• المالية: 6 موظفين\n• العمليات: 4 موظفين\n• التسويق: 3 موظفين",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                Location = new Point(20, 50),
                AutoSize = true
            };

            departmentsGroup.Controls.Add(deptList);
            this.Controls.Add(departmentsGroup);

            // إحصائيات سريعة
            var quickStats = new Guna2GroupBox
            {
                Text = "إحصائيات سريعة",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(220, 53, 69),
                BorderColor = Color.FromArgb(220, 53, 69),
                Size = new Size(200, 200),
                Location = new Point(550, 220)
            };

            var statsText = new Label
            {
                Text = "• موظفين جدد هذا الشهر: 2\n• إجازات معلقة: 3\n• تقييمات مستحقة: 5\n• عقود تنتهي قريباً: 1",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F),
                Location = new Point(15, 50),
                AutoSize = true
            };

            quickStats.Controls.Add(statsText);
            this.Controls.Add(quickStats);
        }
    }
}
