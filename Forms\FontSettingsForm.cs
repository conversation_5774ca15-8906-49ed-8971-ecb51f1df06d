using System;
using System.Drawing;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class FontSettingsForm : Form
    {
        private Guna2TrackBar scaleTrackBar;
        private Label scaleValueLabel;
        private ComboBox fontFamilyCombo;
        private Guna2Button applyButton;
        private Guna2Button resetButton;
        private Label previewLabel;

        public FontSettingsForm()
        {
            InitializeComponent();
            InitializeFontSettings();
        }

        private void InitializeFontSettings()
        {
            this.Text = "إعدادات الخطوط والأحجام";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(34, 33, 74);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Apply responsive design
            Theme.ResponsiveManager.ApplyResponsiveDesign(this);

            CreateFontSettingsUI();
        }

        private void CreateFontSettingsUI()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(Theme.ResponsiveManager.Dimensions.Padding),
                BackColor = Color.FromArgb(34, 33, 74)
            };

            // Title
            var titleLabel = new Label
            {
                Text = "إعدادات الخطوط والأحجام",
                Font = Theme.ResponsiveManager.Fonts.LargeTitle,
                ForeColor = Color.White,
                Location = new Point(20, 20),
                Size = new Size(560, 40),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            // Scale Settings Group
            var scaleGroup = CreateRTLGroupBox("حجم العناصر", Color.FromArgb(52, 152, 219),
                                             new Size(540, 120), new Point(20, 80));

            var scaleLabel = new Label
            {
                Text = "معامل التكبير:",
                Font = Theme.ResponsiveManager.Fonts.Regular,
                ForeColor = Color.White,
                Location = new Point(400, 40),
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            scaleTrackBar = new Guna2TrackBar
            {
                Location = new Point(50, 40),
                Size = new Size(300, 25),
                Minimum = 50,
                Maximum = 200,
                Value = (int)(Theme.ResponsiveManager.ScaleFactor * 100),
                ThumbColor = Color.FromArgb(52, 152, 219),
                FillColor = Color.FromArgb(45, 44, 84),
                RightToLeft = RightToLeft.No
            };

            scaleValueLabel = new Label
            {
                Text = $"{scaleTrackBar.Value}%",
                Font = Theme.ResponsiveManager.Fonts.Regular,
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(360, 40),
                Size = new Size(40, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };

            scaleTrackBar.ValueChanged += (s, e) =>
            {
                scaleValueLabel.Text = $"{scaleTrackBar.Value}%";
                UpdatePreview();
            };

            scaleGroup.Controls.AddRange(new Control[] { scaleLabel, scaleTrackBar, scaleValueLabel });

            // Font Family Group
            var fontGroup = CreateRTLGroupBox("نوع الخط", Color.FromArgb(46, 204, 113),
                                            new Size(540, 80), new Point(20, 220));

            var fontLabel = new Label
            {
                Text = "عائلة الخط:",
                Font = Theme.ResponsiveManager.Fonts.Regular,
                ForeColor = Color.White,
                Location = new Point(400, 40),
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            fontFamilyCombo = new ComboBox
            {
                Location = new Point(50, 40),
                Size = new Size(300, 25),
                Font = Theme.ResponsiveManager.Fonts.Regular,
                BackColor = Color.FromArgb(45, 44, 84),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };

            // Add Arabic-friendly fonts
            fontFamilyCombo.Items.AddRange(new string[]
            {
                "Segoe UI",
                "Tahoma",
                "Arial",
                "Calibri",
                "Times New Roman",
                "Verdana",
                "Microsoft Sans Serif"
            });

            fontFamilyCombo.SelectedItem = Theme.ResponsiveManager.ArabicFontFamily;
            fontFamilyCombo.SelectedIndexChanged += (s, e) => UpdatePreview();

            fontGroup.Controls.AddRange(new Control[] { fontLabel, fontFamilyCombo });

            // Preview Group
            var previewGroup = CreateRTLGroupBox("معاينة", Color.FromArgb(155, 89, 182),
                                               new Size(540, 100), new Point(20, 320));

            previewLabel = new Label
            {
                Text = "هذا نص تجريبي لمعاينة الخط والحجم - This is a sample text",
                Font = Theme.ResponsiveManager.Fonts.Regular,
                ForeColor = Color.White,
                Location = new Point(20, 30),
                Size = new Size(500, 60),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes,
                BorderStyle = BorderStyle.FixedSingle
            };

            previewGroup.Controls.Add(previewLabel);

            // Buttons
            applyButton = new Guna2Button
            {
                Text = "تطبيق التغييرات",
                Size = new Size(150, 40),
                Location = new Point(300, 440),
                FillColor = Color.FromArgb(52, 152, 219),
                HoverState = { FillColor = Color.FromArgb(41, 128, 185) },
                BorderRadius = Theme.ResponsiveManager.Dimensions.BorderRadius,
                Font = Theme.ResponsiveManager.Fonts.Regular,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };

            resetButton = new Guna2Button
            {
                Text = "إعادة تعيين",
                Size = new Size(120, 40),
                Location = new Point(160, 440),
                FillColor = Color.FromArgb(231, 76, 60),
                HoverState = { FillColor = Color.FromArgb(192, 57, 43) },
                BorderRadius = Theme.ResponsiveManager.Dimensions.BorderRadius,
                Font = Theme.ResponsiveManager.Fonts.Regular,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };

            applyButton.Click += ApplyButton_Click;
            resetButton.Click += ResetButton_Click;

            mainPanel.Controls.AddRange(new Control[]
            {
                titleLabel, scaleGroup, fontGroup, previewGroup, applyButton, resetButton
            });

            this.Controls.Add(mainPanel);
        }

        private void UpdatePreview()
        {
            if (previewLabel != null && fontFamilyCombo.SelectedItem != null)
            {
                var scaleFactor = scaleTrackBar.Value / 100f;
                var fontSize = 12f * scaleFactor;

                try
                {
                    var font = new Font(fontFamilyCombo.SelectedItem.ToString(), fontSize, FontStyle.Regular);
                    previewLabel.Font = font;
                }
                catch
                {
                    // Fallback to default font if selected font fails
                    previewLabel.Font = new Font("Tahoma", fontSize, FontStyle.Regular);
                }
            }
        }

        private void ApplyButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Update ResponsiveManager settings
                var newScaleFactor = scaleTrackBar.Value / 100f;
                var newFontFamily = fontFamilyCombo.SelectedItem?.ToString() ?? "Segoe UI";

                // Update settings (in a real app, save to config file)
                Theme.ResponsiveManager.ArabicFontFamily = newFontFamily;

                // Trigger responsive update
                Theme.ResponsiveManager.OnScreenSizeChanged();

                MessageBox.Show("تم تطبيق الإعدادات بنجاح!\nسيتم تطبيق التغييرات على النوافذ الجديدة.",
                              "نجح التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تطبيق الإعدادات:\n{ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ResetButton_Click(object sender, EventArgs e)
        {
            scaleTrackBar.Value = 100;
            fontFamilyCombo.SelectedItem = "Segoe UI";
            UpdatePreview();
        }

        private Guna2GroupBox CreateRTLGroupBox(string text, Color borderColor, Size size, Point location)
        {
            var groupBox = new Guna2GroupBox
            {
                Text = text,
                Font = Theme.ResponsiveManager.Fonts.Subtitle,
                ForeColor = Color.White,
                CustomBorderColor = borderColor,
                BorderColor = borderColor,
                Size = size,
                Location = location,
                Padding = new Padding(Theme.ResponsiveManager.Dimensions.SmallPadding),
                RightToLeft = RightToLeft.Yes
            };

            return groupBox;
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.ResumeLayout(false);
        }
    }
}
