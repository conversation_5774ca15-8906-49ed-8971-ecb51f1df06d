﻿using System.Windows.Forms;

namespace ModernWinApp
{
    partial class Form1
    {
        private System.ComponentModel.IContainer components = null;
        private Controls.DoubleBufferedPanel panelMain;
        private Controls.DoubleBufferedPanel panelMenu;
        private FontAwesome.Sharp.IconButton btnSettings;
        private FontAwesome.Sharp.IconButton btnSalaries;
        private FontAwesome.Sharp.IconButton btnAttendance;
        private FontAwesome.Sharp.IconButton btnEmployees;
        private FontAwesome.Sharp.IconButton btnDashboard;
        private Panel panelLogo;
        private Label lblLogo;
        private Panel panelTitleBar;
        private Label lblTitle;
        private Panel panelControls;
        private Guna.UI2.WinForms.Guna2ControlBox btnMinimize;
        private Guna.UI2.WinForms.Guna2ControlBox btnMaximize;
        private Guna.UI2.WinForms.Guna2ControlBox btnClose;
        private Controls.DoubleBufferedPanel panelDesktop;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            InitializeControls();
            SetupLayout();
            SetupEvents();
        }

        private void InitializeControls()
        {
            components = new System.ComponentModel.Container();

            panelMain = new Controls.DoubleBufferedPanel();
            panelMenu = new Controls.DoubleBufferedPanel { Name = "panelMenu" };
            btnSettings = new FontAwesome.Sharp.IconButton();
            btnSalaries = new FontAwesome.Sharp.IconButton();
            btnAttendance = new FontAwesome.Sharp.IconButton();
            btnEmployees = new FontAwesome.Sharp.IconButton();
            btnDashboard = new FontAwesome.Sharp.IconButton();
            panelLogo = new Panel();
            lblLogo = new Label();
            panelTitleBar = new Panel();
            lblTitle = new Label();
            panelControls = new Panel();
            btnMinimize = new Guna.UI2.WinForms.Guna2ControlBox();
            btnMaximize = new Guna.UI2.WinForms.Guna2ControlBox();
            btnClose = new Guna.UI2.WinForms.Guna2ControlBox();
            panelDesktop = new Controls.DoubleBufferedPanel();

            SuspendLayout();
            panelMain.SuspendLayout();
            panelMenu.SuspendLayout();
            panelLogo.SuspendLayout();
            panelTitleBar.SuspendLayout();
            panelControls.SuspendLayout();
        }

        private void SetupEvents()
        {
            btnDashboard.Click += new EventHandler((s, e) => btnDashboard_Click(s, e));
            btnEmployees.Click += new EventHandler((s, e) => btnEmployees_Click(s, e));
            btnAttendance.Click += new EventHandler((s, e) => btnAttendance_Click(s, e));
            btnSalaries.Click += new EventHandler((s, e) => btnSalaries_Click(s, e));
            btnSettings.Click += new EventHandler((s, e) => btnSettings_Click(s, e));
            panelTitleBar.MouseDown += new MouseEventHandler((s, e) => panelTitleBar_MouseDown(s, e));
            Resize += new EventHandler((s, e) => Form1_Resize(s, e));
        }

        private void Form1_Resize(object sender, EventArgs e)
        {
            // Always keep padding at 0 to avoid white margins
            Padding = new Padding(0);
        }

        private void SetupLayout()
        {
            AutoScaleDimensions = new SizeF(8F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1200, 700);
            MinimumSize = new Size(Theme.ThemeManager.Dimensions.MinimumWidth,
                                 Theme.ThemeManager.Dimensions.MinimumHeight);
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            FormBorderStyle = FormBorderStyle.None;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "نظام موافق لإدارة شؤون الموظفين";

            SetupMainPanel();
            SetupMenu();
            SetupTitleBar();
            SetupDesktop();

            Controls.Add(panelMain);

            panelMain.ResumeLayout(false);
            panelMenu.ResumeLayout(false);
            panelLogo.ResumeLayout(false);
            panelTitleBar.ResumeLayout(false);
            panelTitleBar.PerformLayout();
            panelControls.ResumeLayout(false);
            ResumeLayout(false);
        }

        private void SetupMainPanel()
        {
            panelMain.Dock = DockStyle.Fill;
            panelMain.Padding = new Padding(0);

            panelMain.Controls.Add(panelDesktop);
            panelMain.Controls.Add(panelMenu);
            panelMain.Controls.Add(panelTitleBar);
        }

        private void SetupMenu()
        {
            panelMenu.Width = Theme.ThemeManager.Dimensions.SidebarWidth;
            panelMenu.Dock = DockStyle.Right;
            panelMenu.BackColor = Theme.ThemeManager.Colors.Sidebar;
            panelMenu.Padding = new Padding(Theme.ThemeManager.Dimensions.Padding);

            SetupMenuButton(btnSettings, "إعدادات النظام", FontAwesome.Sharp.IconChar.Gear, 380);
            SetupMenuButton(btnSalaries, "إدارة الرواتب", FontAwesome.Sharp.IconChar.DollarSign, 320);
            SetupMenuButton(btnAttendance, "تتبع الحضور", FontAwesome.Sharp.IconChar.CalendarCheck, 260);
            SetupMenuButton(btnEmployees, "إدارة الموظفين", FontAwesome.Sharp.IconChar.Users, 200);
            SetupMenuButton(btnDashboard, "لوحة المعلومات", FontAwesome.Sharp.IconChar.ChartLine, 140);

            panelMenu.Controls.Add(btnSettings);
            panelMenu.Controls.Add(btnSalaries);
            panelMenu.Controls.Add(btnAttendance);
            panelMenu.Controls.Add(btnEmployees);
            panelMenu.Controls.Add(btnDashboard);
            panelMenu.Controls.Add(panelLogo);

            panelLogo.Dock = DockStyle.Top;
            panelLogo.Height = 140;
            panelLogo.BackColor = Theme.ThemeManager.Colors.Sidebar;

            lblLogo.Dock = DockStyle.Fill;
            lblLogo.Font = Theme.ThemeManager.Fonts.MenuTitle;
            lblLogo.ForeColor = Theme.ThemeManager.Colors.White;
            lblLogo.Text = "موافق";
            lblLogo.TextAlign = ContentAlignment.MiddleCenter;
            lblLogo.RightToLeft = RightToLeft.Yes;
        }

        private void SetupMenuButton(FontAwesome.Sharp.IconButton button, string text, FontAwesome.Sharp.IconChar icon, int top)
        {
            button.Dock = DockStyle.Top;
            button.FlatAppearance.BorderSize = 0;
            button.FlatStyle = FlatStyle.Flat;
            button.IconChar = icon;
            button.IconFont = FontAwesome.Sharp.IconFont.Auto;
            button.IconSize = Theme.ResponsiveManager.Dimensions.MediumIconSize;
            button.ImageAlign = ContentAlignment.MiddleLeft;
            button.Location = new Point(0, top);
            button.Padding = new Padding(Theme.ResponsiveManager.Dimensions.Padding, 0,
                                       Theme.ResponsiveManager.Dimensions.SmallPadding, 0);
            button.Height = Theme.ResponsiveManager.Dimensions.MenuButtonHeight;
            button.Text = text;
            button.TextAlign = ContentAlignment.MiddleRight;
            button.TextImageRelation = TextImageRelation.TextBeforeImage;
            button.UseVisualStyleBackColor = true;
            button.Cursor = Cursors.Hand;
            button.Font = Theme.ThemeManager.Fonts.MenuButton;
            button.ForeColor = Theme.ThemeManager.Colors.White;
            button.IconColor = Theme.ThemeManager.Colors.White;
            button.BackColor = Theme.ThemeManager.Colors.Sidebar;
            button.RightToLeft = RightToLeft.Yes;
        }

        private void SetupTitleBar()
        {
            panelTitleBar.Height = Theme.ThemeManager.Dimensions.TopBarHeight;
            panelTitleBar.Dock = DockStyle.Top;
            panelTitleBar.BackColor = Theme.ThemeManager.Colors.Secondary;

            lblTitle.AutoSize = true;
            lblTitle.Font = Theme.ThemeManager.Fonts.Title;
            lblTitle.ForeColor = Theme.ThemeManager.Colors.Text;
            lblTitle.Location = new Point(720, 23);
            lblTitle.Text = "الموظفين";
            lblTitle.TextAlign = ContentAlignment.MiddleRight;
            lblTitle.Dock = DockStyle.Right;
            lblTitle.Padding = new Padding(0, Theme.ResponsiveManager.Dimensions.Padding,
                                         Theme.ResponsiveManager.Dimensions.Padding, 0);
            lblTitle.RightToLeft = RightToLeft.Yes;

            SetupWindowControls();

            panelTitleBar.Controls.Add(lblTitle);
            panelTitleBar.Controls.Add(panelControls);
        }

        private void SetupWindowControls()
        {
            panelControls.Dock = DockStyle.Left;
            panelControls.Width = 120;
            panelControls.RightToLeft = RightToLeft.No;

            btnClose.Size = btnMaximize.Size = btnMinimize.Size = new Size(40, 35);
            btnClose.Anchor = btnMaximize.Anchor = btnMinimize.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            btnClose.BorderRadius = btnMaximize.BorderRadius = btnMinimize.BorderRadius = Theme.ThemeManager.Dimensions.BorderRadius;

            btnClose.Location = new Point(80, 20);
            btnMaximize.Location = new Point(40, 20);
            btnMaximize.ControlBoxType = Guna.UI2.WinForms.Enums.ControlBoxType.MaximizeBox;
            btnMinimize.Location = new Point(0, 20);
            btnMinimize.ControlBoxType = Guna.UI2.WinForms.Enums.ControlBoxType.MinimizeBox;

            panelControls.Controls.Add(btnClose);
            panelControls.Controls.Add(btnMaximize);
            panelControls.Controls.Add(btnMinimize);
        }

        private void SetupDesktop()
        {
            panelDesktop.Dock = DockStyle.Fill;
            panelDesktop.Padding = new Padding(Theme.ThemeManager.Dimensions.Padding);
            panelDesktop.BackColor = Theme.ThemeManager.Colors.Background;
        }
    }
}
