using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class FormProducts : Form
    {
        public FormProducts()
        {
            InitializeComponent();
            InitializeProducts();
        }

        private void InitializeProducts()
        {
            var addButton = new Guna2Button
            {
                Text = "إضافة منتج",
                FillColor = Color.FromArgb(0, 126, 249),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Size = new Size(150, 45),
                Location = new Point(20, 20),
                BorderRadius = 5
            };
            this.Controls.Add(addButton);

            var grid = new Guna2DataGridView
            {
                Location = new Point(20, 80),
                Size = new Size(760, 350),
                BackgroundColor = Color.FromArgb(34, 33, 74),
                ThemeStyle = {
                    HeaderStyle = { BackColor = Color.FromArgb(37, 36, 81), ForeColor = Color.White },
                    RowsStyle = { BackColor = Color.FromArgb(34, 33, 74), ForeColor = Color.White }
                }
            };

            grid.Columns.Add("ProductId", "رقم المنتج");
            grid.Columns.Add("Name", "اسم المنتج");
            grid.Columns.Add("Price", "السعر");
            grid.Columns.Add("Category", "التصنيف");
            grid.Columns.Add("Stock", "المخزون");

            // Add sample data
            grid.Rows.Add("P001", "حاسوب محمول", "3500 ريال", "إلكترونيات", "15");
            grid.Rows.Add("P002", "هاتف ذكي", "2000 ريال", "إلكترونيات", "25");
            grid.Rows.Add("P003", "سماعات لاسلكية", "300 ريال", "اكسسوارات", "50");

            this.Controls.Add(grid);
        }
    }
}