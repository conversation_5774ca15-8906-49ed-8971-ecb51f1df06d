using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class FormDashboard : Form
    {
        public FormDashboard()
        {
            InitializeComponent();
            InitializeDashboard();
        }

        private void InitializeDashboard()
        {
            this.BackColor = Color.FromArgb(34, 33, 74);

            var card1 = new Guna2GroupBox
            {
                Text = "إجمالي المبيعات",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(0, 126, 249),
                BorderColor = Color.FromArgb(0, 126, 249),
                Size = new Size(200, 150),
                Location = new Point(30, 30)
            };

            var lblSales = new Label
            {
                Text = "10,500 ريال",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Location = new Point(50, 70),
                AutoSize = true
            };

            card1.Controls.Add(lblSales);
            this.Controls.Add(card1);

            var chart = new Guna2CircleProgressBar
            {
                Value = 75,
                Size = new Size(150, 150),
                Location = new Point(260, 30),
                FillColor = Color.FromArgb(26, 25, 62),
                ProgressColor = Color.FromArgb(0, 126, 249),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold)
            };

            chart.Text = "75%";
            this.Controls.Add(chart);
        }
    }
}