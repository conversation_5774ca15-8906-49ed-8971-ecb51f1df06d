using System.Drawing;

namespace ModernWinApp.Theme
{
    public static class ThemeManager
    {
        public static bool IsDarkMode { get; private set; } = true;

        public static class Colors
        {
            // Dark Mode Colors
            public static readonly Color DarkBackground = ColorTranslator.FromHtml("#1A1A2E");
            public static readonly Color DarkSecondary = ColorTranslator.FromHtml("#16213E");
            public static readonly Color DarkSidebar = ColorTranslator.FromHtml("#0F172A");
            public static readonly Color DarkBorder = ColorTranslator.FromHtml("#1E293B");
            public static readonly Color DarkAccent = ColorTranslator.FromHtml("#2563EB");
            public static readonly Color DarkHover = ColorTranslator.FromHtml("#1E40AF");
            public static readonly Color DarkSelection = ColorTranslator.FromHtml("#1D4ED8");
            
            // Light Mode Colors
            public static readonly Color LightBackground = ColorTranslator.FromHtml("#F3F3F3");
            public static readonly Color LightSecondary = ColorTranslator.FromHtml("#FFFFFF");
            public static readonly Color LightSidebar = ColorTranslator.FromHtml("#F8FAFC");
            public static readonly Color LightBorder = ColorTranslator.FromHtml("#E2E8F0");
            public static readonly Color LightAccent = ColorTranslator.FromHtml("#2563EB");
            public static readonly Color LightHover = ColorTranslator.FromHtml("#DBEAFE");
            public static readonly Color LightSelection = ColorTranslator.FromHtml("#BFDBFE");
            
            // Common Colors
            public static readonly Color White = ColorTranslator.FromHtml("#FFFFFF");
            public static readonly Color Black = ColorTranslator.FromHtml("#000000");
            public static readonly Color Success = ColorTranslator.FromHtml("#10B981");
            public static readonly Color Warning = ColorTranslator.FromHtml("#F59E0B");
            public static readonly Color Danger = ColorTranslator.FromHtml("#EF4444");

            // Current Theme Colors
            public static Color Background => IsDarkMode ? DarkBackground : LightBackground;
            public static Color Secondary => IsDarkMode ? DarkSecondary : LightSecondary;
            public static Color Sidebar => IsDarkMode ? DarkSidebar : LightSidebar;
            public static Color Border => IsDarkMode ? DarkBorder : LightBorder;
            public static Color Text => IsDarkMode ? White : Black;
            public static Color TextSecondary => IsDarkMode ? Color.FromArgb(200, White) : Color.FromArgb(100, Black);
            public static Color Accent => IsDarkMode ? DarkAccent : LightAccent;
            public static Color Selection => IsDarkMode ? DarkSelection : LightSelection;
            public static Color Hover => IsDarkMode ? DarkHover : LightHover;
        }

        public static class Fonts
        {
            public static readonly Font Title = new Font("Segoe UI", 12F, FontStyle.Bold);
            public static readonly Font Subtitle = new Font("Segoe UI", 10F, FontStyle.Bold);
            public static readonly Font Regular = new Font("Segoe UI", 9F, FontStyle.Regular);
            public static readonly Font Small = new Font("Segoe UI", 8F, FontStyle.Regular);
        }

        public static class Dimensions
        {
            public const int BorderRadius = 8;
            public const int Padding = 20;
            public const int SidebarWidth = 250;
            public const int TopBarHeight = 75;
            public const int ButtonHeight = 45;
            public const int GridRowHeight = 55;
            public const int GridHeaderHeight = 60;
            public const int MinimumWidth = 1000;
            public const int MinimumHeight = 600;
        }

        public static void ToggleTheme()
        {
            IsDarkMode = !IsDarkMode;
            ThemeChanged?.Invoke(null, EventArgs.Empty);
        }

        public static event EventHandler ThemeChanged;

        public static class Effects
        {
            public static class DataGrid
            {
                public static Color RowBackground => IsDarkMode ? Colors.DarkBackground : Colors.LightSecondary;
                public static Color AlternateRowBackground => IsDarkMode ? Colors.DarkSecondary : Color.FromArgb(250, 250, 250);
                public static Color HeaderBackground => IsDarkMode ? Colors.DarkSidebar : Color.FromArgb(240, 240, 240);
                public static Color SelectionBackground => IsDarkMode ? Colors.DarkSelection : Colors.LightSelection;
                public static Color CellBorder => IsDarkMode ? Colors.DarkBorder : Colors.LightBorder;
            }
        }

        public static Guna.UI2.WinForms.Guna2Button CreateFluentButton()
        {
            var btn = new Guna.UI2.WinForms.Guna2Button
            {
                BorderRadius = Dimensions.BorderRadius,
                FillColor = Colors.Accent,
                Font = Fonts.Regular,
                ForeColor = Colors.White,
                Height = Dimensions.ButtonHeight,
                Cursor = Cursors.Hand,
                Animated = true
            };

            btn.HoverState.FillColor = Colors.Hover;
            btn.DisabledState.FillColor = Color.FromArgb(120, Colors.Accent);

            return btn;
        }

        public static Guna.UI2.WinForms.Guna2TextBox CreateFluentTextBox()
        {
            var txt = new Guna.UI2.WinForms.Guna2TextBox
            {
                BorderRadius = Dimensions.BorderRadius,
                FillColor = Colors.Secondary,
                Font = Fonts.Regular,
                ForeColor = Colors.Text,
                BorderColor = Colors.Border
            };

            txt.PlaceholderForeColor = Colors.TextSecondary;
            return txt;
        }

        public static void ApplyTheme(Form form)
        {
            form.BackColor = Colors.Background;
            foreach (Control control in form.Controls)
            {
                ApplyThemeToControl(control);
            }
        }

        private static void ApplyThemeToControl(Control control)
        {
            if (control is Panel panel)
            {
                if (panel.Name == "panelMenu")
                    panel.BackColor = Colors.Sidebar;
                else
                    panel.BackColor = Colors.Secondary;

                foreach (Control child in panel.Controls)
                {
                    ApplyThemeToControl(child);
                }
            }
            else if (control is Label label)
            {
                label.ForeColor = Colors.Text;
            }
            else if (control is Guna.UI2.WinForms.Guna2DataGridView grid)
            {
                grid.BackgroundColor = Colors.Secondary;
                grid.GridColor = Colors.Border;
                grid.DefaultCellStyle.BackColor = Effects.DataGrid.RowBackground;
                grid.DefaultCellStyle.ForeColor = Colors.Text;
                grid.DefaultCellStyle.SelectionBackColor = Effects.DataGrid.SelectionBackground;
                grid.DefaultCellStyle.SelectionForeColor = Colors.Text;
                grid.ColumnHeadersDefaultCellStyle.BackColor = Effects.DataGrid.HeaderBackground;
                grid.ColumnHeadersDefaultCellStyle.ForeColor = Colors.Text;
                grid.ColumnHeadersDefaultCellStyle.SelectionBackColor = Effects.DataGrid.HeaderBackground;
                grid.AlternatingRowsDefaultCellStyle.BackColor = Effects.DataGrid.AlternateRowBackground;
                grid.AlternatingRowsDefaultCellStyle.SelectionBackColor = Effects.DataGrid.SelectionBackground;
                grid.AlternatingRowsDefaultCellStyle.SelectionForeColor = Colors.Text;
                grid.RowTemplate.Height = Dimensions.GridRowHeight;
            }
        }
    }
}