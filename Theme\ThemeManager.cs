using System.Drawing;

namespace ModernWinApp.Theme
{
    public static class ThemeManager
    {
        public static bool IsDarkMode { get; private set; } = true;

        public static class Colors
        {
            // Modern Color System using ModernColorScheme
            public static Color Primary => ModernColorScheme.GetPrimary();
            public static Color Secondary => ModernColorScheme.GetSecondary();
            public static Color Accent => ModernColorScheme.GetAccent();
            public static Color AccentLight => ModernColorScheme.GetAccentLight();
            public static Color AccentDark => ModernColorScheme.GetAccentDark();
            public static Color Surface => ModernColorScheme.GetSurface();
            public static Color Background => ModernColorScheme.GetBackground();
            public static Color Card => ModernColorScheme.GetCard();
            public static Color Sidebar => ModernColorScheme.GetSidebar();

            // Status Colors
            public static Color Success => ModernColorScheme.GetSuccess();
            public static Color Warning => ModernColorScheme.GetWarning();
            public static Color Error => ModernColorScheme.GetError();
            public static Color Info => ModernColorScheme.GetInfo();
            public static Color Danger => ModernColorScheme.GetError(); // Alias for compatibility

            // Text Colors
            public static Color Text => ModernColorScheme.TextPrimary;
            public static Color TextSecondary => ModernColorScheme.TextSecondary;
            public static Color TextMuted => ModernColorScheme.TextMuted;
            public static Color TextDisabled => ModernColorScheme.TextDisabled;

            // Interactive Colors
            public static Color Hover => AccentLight;
            public static Color Selection => AccentDark;
            public static Color Border => Surface;

            // Legacy Colors for compatibility
            public static Color White => Color.White;
            public static Color Black => Color.Black;

            // Gradient Support
            public static Color[] AccentGradient => ModernColorScheme.Gradients.AccentGradient;
            public static Color[] BackgroundGradient => ModernColorScheme.Gradients.OceanGradient;
            public static Color[] SuccessGradient => ModernColorScheme.Gradients.SuccessGradient;

            // Special UI Colors
            public static Color MenuActive => AccentDark;
            public static Color MenuHover => Color.FromArgb(30, AccentLight);
            public static Color CardShadow => Color.FromArgb(20, 0, 0, 0);
            public static Color Overlay => Color.FromArgb(120, 0, 0, 0);

            // Input Colors
            public static Color InputBackground => Card;
            public static Color InputBorder => Surface;
            public static Color InputFocus => Accent;
            public static Color InputPlaceholder => TextMuted;
        }

        public static class Fonts
        {
            // Use ResponsiveManager fonts for better Arabic support and scaling
            public static Font Title => ResponsiveManager.Fonts.Title;
            public static Font Subtitle => ResponsiveManager.Fonts.Subtitle;
            public static Font Regular => ResponsiveManager.Fonts.Regular;
            public static Font Small => ResponsiveManager.Fonts.Small;
            public static Font MenuButton => ResponsiveManager.Fonts.MenuButton;
            public static Font MenuTitle => ResponsiveManager.Fonts.MenuTitle;
        }

        public static class Dimensions
        {
            // Use ResponsiveManager dimensions for better scaling
            public static int BorderRadius => ResponsiveManager.Dimensions.BorderRadius;
            public static int Padding => ResponsiveManager.Dimensions.Padding;
            public static int SidebarWidth => ResponsiveManager.Dimensions.SidebarWidth;
            public static int TopBarHeight => ResponsiveManager.Dimensions.TopBarHeight;
            public static int ButtonHeight => ResponsiveManager.Dimensions.ButtonHeight;
            public static int GridRowHeight => ResponsiveManager.Dimensions.GridRowHeight;
            public static int GridHeaderHeight => ResponsiveManager.Dimensions.GridHeaderHeight;
            public static int MinimumWidth => ResponsiveManager.Dimensions.MinimumWidth;
            public static int MinimumHeight => ResponsiveManager.Dimensions.MinimumHeight;
        }

        public static void ToggleTheme()
        {
            IsDarkMode = !IsDarkMode;
            ThemeChanged?.Invoke(null, EventArgs.Empty);
        }

        public static event EventHandler ThemeChanged;

        public static class Effects
        {
            public static class DataGrid
            {
                public static Color RowBackground => Colors.Card;
                public static Color AlternateRowBackground => Colors.Secondary;
                public static Color HeaderBackground => Colors.Surface;
                public static Color SelectionBackground => Colors.Selection;
                public static Color CellBorder => Colors.Border;
                public static Color HoverBackground => Colors.MenuHover;
            }

            public static class Cards
            {
                public static Color Background => Colors.Card;
                public static Color Border => Colors.Border;
                public static Color Shadow => Colors.CardShadow;
                public static Color HoverShadow => Color.FromArgb(40, 0, 0, 0);
            }

            public static class Buttons
            {
                public static Color Primary => Colors.Accent;
                public static Color PrimaryHover => Colors.AccentLight;
                public static Color PrimaryActive => Colors.AccentDark;
                public static Color Secondary => Colors.Surface;
                public static Color SecondaryHover => Colors.Border;
                public static Color Success => Colors.Success;
                public static Color Warning => Colors.Warning;
                public static Color Error => Colors.Error;
            }
        }

        public static Guna.UI2.WinForms.Guna2Button CreateFluentButton()
        {
            var btn = new Guna.UI2.WinForms.Guna2Button
            {
                BorderRadius = Dimensions.BorderRadius,
                FillColor = Colors.Accent,
                Font = Fonts.Regular,
                ForeColor = Colors.White,
                Height = Dimensions.ButtonHeight,
                Cursor = Cursors.Hand,
                Animated = true
            };

            btn.HoverState.FillColor = Colors.Hover;
            btn.DisabledState.FillColor = Color.FromArgb(120, Colors.Accent);

            return btn;
        }

        public static Guna.UI2.WinForms.Guna2TextBox CreateFluentTextBox()
        {
            var txt = new Guna.UI2.WinForms.Guna2TextBox
            {
                BorderRadius = Dimensions.BorderRadius,
                FillColor = Colors.Secondary,
                Font = Fonts.Regular,
                ForeColor = Colors.Text,
                BorderColor = Colors.Border
            };

            txt.PlaceholderForeColor = Colors.TextSecondary;
            return txt;
        }

        public static void ApplyTheme(Form form)
        {
            form.BackColor = Colors.Background;

            // Apply responsive design first
            ResponsiveManager.ApplyResponsiveDesign(form);

            foreach (Control control in form.Controls)
            {
                ApplyThemeToControl(control);
            }
        }

        private static void ApplyThemeToControl(Control control)
        {
            if (control is Panel panel)
            {
                if (panel.Name == "panelMenu")
                    panel.BackColor = Colors.Sidebar;
                else
                    panel.BackColor = Colors.Secondary;

                foreach (Control child in panel.Controls)
                {
                    ApplyThemeToControl(child);
                }
            }
            else if (control is Label label)
            {
                label.ForeColor = Colors.Text;
            }
            else if (control is Guna.UI2.WinForms.Guna2DataGridView grid)
            {
                grid.BackgroundColor = Colors.Secondary;
                grid.GridColor = Colors.Border;
                grid.DefaultCellStyle.BackColor = Effects.DataGrid.RowBackground;
                grid.DefaultCellStyle.ForeColor = Colors.Text;
                grid.DefaultCellStyle.SelectionBackColor = Effects.DataGrid.SelectionBackground;
                grid.DefaultCellStyle.SelectionForeColor = Colors.Text;
                grid.ColumnHeadersDefaultCellStyle.BackColor = Effects.DataGrid.HeaderBackground;
                grid.ColumnHeadersDefaultCellStyle.ForeColor = Colors.Text;
                grid.ColumnHeadersDefaultCellStyle.SelectionBackColor = Effects.DataGrid.HeaderBackground;
                grid.AlternatingRowsDefaultCellStyle.BackColor = Effects.DataGrid.AlternateRowBackground;
                grid.AlternatingRowsDefaultCellStyle.SelectionBackColor = Effects.DataGrid.SelectionBackground;
                grid.AlternatingRowsDefaultCellStyle.SelectionForeColor = Colors.Text;
                grid.RowTemplate.Height = Dimensions.GridRowHeight;
            }
        }
    }
}