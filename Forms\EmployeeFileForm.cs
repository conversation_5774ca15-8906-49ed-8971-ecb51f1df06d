using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class EmployeeFileForm : Form
    {
        private string employeeId;
        private string employeeName;

        public EmployeeFileForm(string empId, string empName)
        {
            employeeId = empId;
            employeeName = empName;
            InitializeComponent();
            InitializeEmployeeFile();
        }

        private void InitializeEmployeeFile()
        {
            this.Text = $"ملف الموظف - {employeeName}";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(34, 33, 74);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إنشاء اللوحة الرئيسية
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                AutoScroll = true
            };

            // إنشاء شريط العنوان
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(40, 39, 84),
                Padding = new Padding(20)
            };

            var titleLabel = new Label
            {
                Text = $"📁 ملف الموظف - {employeeName}",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Dock = DockStyle.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                AutoSize = false,
                Width = 450
            };

            var closeButton = new Guna2Button
            {
                Text = "إغلاق",
                Size = new Size(100, 40),
                Dock = DockStyle.Right,
                FillColor = Color.FromArgb(220, 53, 69),
                HoverState = { FillColor = Color.FromArgb(200, 35, 51) },
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            closeButton.Click += (s, e) => this.Close();

            headerPanel.Controls.Add(titleLabel);
            headerPanel.Controls.Add(closeButton);

            // إنشاء لوحة المحتوى
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // إنشاء التخطيط بعمودين
            var leftPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 450,
                Padding = new Padding(10)
            };

            var rightPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            // إنشاء قسم المعلومات الشخصية
            CreatePersonalInfoSection(leftPanel);

            // إنشاء قسم معلومات العمل
            CreateWorkInfoSection(leftPanel);

            // إنشاء قسم الراتب والمزايا
            CreateSalarySection(rightPanel);

            // إنشاء قسم الحضور والأداء
            CreatePerformanceSection(rightPanel);

            contentPanel.Controls.Add(rightPanel);
            contentPanel.Controls.Add(leftPanel);

            mainPanel.Controls.Add(contentPanel);
            mainPanel.Controls.Add(headerPanel);

            this.Controls.Add(mainPanel);
        }

        private void CreatePersonalInfoSection(Panel parent)
        {
            var personalGroup = new Guna2GroupBox
            {
                Text = "المعلومات الشخصية",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(0, 126, 249),
                BorderColor = Color.FromArgb(0, 126, 249),
                Size = new Size(420, 280),
                Location = new Point(10, 10),
                Padding = new Padding(15)
            };

            // صورة الموظف
            var photoPanel = new Panel
            {
                Size = new Size(100, 100),
                Location = new Point(20, 40),
                BackColor = Color.FromArgb(50, 49, 94),
                BorderStyle = BorderStyle.FixedSingle
            };

            var photoLabel = new Label
            {
                Text = "👤",
                Font = new Font("Segoe UI", 48F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            photoPanel.Controls.Add(photoLabel);

            // المعلومات الأساسية
            var nameLabel = CreateInfoLabel("الاسم الكامل:", employeeName, 140, 50);
            var idLabel = CreateInfoLabel("الرقم الوظيفي:", employeeId, 140, 80);
            var phoneLabel = CreateInfoLabel("رقم الهاتف:", GetEmployeeData(employeeId, "phone"), 140, 110);
            var emailLabel = CreateInfoLabel("البريد الإلكتروني:", GetEmployeeData(employeeId, "email"), 140, 140);
            var nationalIdLabel = CreateInfoLabel("رقم الهوية:", GetEmployeeData(employeeId, "nationalId"), 140, 170);
            var birthDateLabel = CreateInfoLabel("تاريخ الميلاد:", GetEmployeeData(employeeId, "birthDate"), 140, 200);
            var addressLabel = CreateInfoLabel("العنوان:", GetEmployeeData(employeeId, "address"), 140, 230);

            personalGroup.Controls.AddRange(new Control[] {
                photoPanel, nameLabel.label, nameLabel.value,
                idLabel.label, idLabel.value,
                phoneLabel.label, phoneLabel.value,
                emailLabel.label, emailLabel.value,
                nationalIdLabel.label, nationalIdLabel.value,
                birthDateLabel.label, birthDateLabel.value,
                addressLabel.label, addressLabel.value
            });

            parent.Controls.Add(personalGroup);
        }

        private void CreateWorkInfoSection(Panel parent)
        {
            var workGroup = new Guna2GroupBox
            {
                Text = "معلومات العمل",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(40, 167, 69),
                BorderColor = Color.FromArgb(40, 167, 69),
                Size = new Size(420, 220),
                Location = new Point(10, 300),
                Padding = new Padding(15)
            };

            var departmentLabel = CreateInfoLabel("القسم:", GetEmployeeData(employeeId, "department"), 20, 40);
            var positionLabel = CreateInfoLabel("المنصب:", GetEmployeeData(employeeId, "position"), 20, 70);
            var joinDateLabel = CreateInfoLabel("تاريخ التعيين:", GetEmployeeData(employeeId, "joinDate"), 20, 100);
            var managerLabel = CreateInfoLabel("المدير المباشر:", GetEmployeeData(employeeId, "manager"), 20, 130);
            var contractLabel = CreateInfoLabel("نوع العقد:", GetEmployeeData(employeeId, "contractType"), 20, 160);

            workGroup.Controls.AddRange(new Control[] {
                departmentLabel.label, departmentLabel.value,
                positionLabel.label, positionLabel.value,
                joinDateLabel.label, joinDateLabel.value,
                managerLabel.label, managerLabel.value,
                contractLabel.label, contractLabel.value
            });

            parent.Controls.Add(workGroup);
        }

        private void CreateSalarySection(Panel parent)
        {
            var salaryGroup = new Guna2GroupBox
            {
                Text = "الراتب والمزايا",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(255, 193, 7),
                BorderColor = Color.FromArgb(255, 193, 7),
                Size = new Size(420, 250),
                Location = new Point(10, 10),
                Padding = new Padding(15)
            };

            var basicSalaryLabel = CreateInfoLabel("الراتب الأساسي:", GetEmployeeData(employeeId, "basicSalary"), 20, 40);
            var allowancesLabel = CreateInfoLabel("البدلات:", GetEmployeeData(employeeId, "allowances"), 20, 70);
            var insuranceLabel = CreateInfoLabel("التأمين الطبي:", GetEmployeeData(employeeId, "insurance"), 20, 100);
            var vacationLabel = CreateInfoLabel("الإجازات السنوية:", GetEmployeeData(employeeId, "vacation"), 20, 130);
            var bonusLabel = CreateInfoLabel("المكافآت:", GetEmployeeData(employeeId, "bonus"), 20, 160);
            var netSalaryLabel = CreateInfoLabel("صافي الراتب:", GetEmployeeData(employeeId, "netSalary"), 20, 190);

            salaryGroup.Controls.AddRange(new Control[] {
                basicSalaryLabel.label, basicSalaryLabel.value,
                allowancesLabel.label, allowancesLabel.value,
                insuranceLabel.label, insuranceLabel.value,
                vacationLabel.label, vacationLabel.value,
                bonusLabel.label, bonusLabel.value,
                netSalaryLabel.label, netSalaryLabel.value
            });

            parent.Controls.Add(salaryGroup);
        }

        private void CreatePerformanceSection(Panel parent)
        {
            var performanceGroup = new Guna2GroupBox
            {
                Text = "الحضور والأداء",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                CustomBorderColor = Color.FromArgb(220, 53, 69),
                BorderColor = Color.FromArgb(220, 53, 69),
                Size = new Size(420, 250),
                Location = new Point(10, 270),
                Padding = new Padding(15)
            };

            var attendanceLabel = CreateInfoLabel("معدل الحضور:", GetEmployeeData(employeeId, "attendance"), 20, 40);
            var lateLabel = CreateInfoLabel("مرات التأخير:", GetEmployeeData(employeeId, "lateCount"), 20, 70);
            var absenceLabel = CreateInfoLabel("أيام الغياب:", GetEmployeeData(employeeId, "absenceCount"), 20, 100);
            var overtimeLabel = CreateInfoLabel("ساعات إضافية:", GetEmployeeData(employeeId, "overtime"), 20, 130);
            var ratingLabel = CreateInfoLabel("تقييم الأداء:", GetEmployeeData(employeeId, "rating"), 20, 160);
            var lastReviewLabel = CreateInfoLabel("آخر مراجعة:", GetEmployeeData(employeeId, "lastReview"), 20, 190);

            performanceGroup.Controls.AddRange(new Control[] {
                attendanceLabel.label, attendanceLabel.value,
                lateLabel.label, lateLabel.value,
                absenceLabel.label, absenceLabel.value,
                overtimeLabel.label, overtimeLabel.value,
                ratingLabel.label, ratingLabel.value,
                lastReviewLabel.label, lastReviewLabel.value
            });

            parent.Controls.Add(performanceGroup);
        }

        private (Label label, Label value) CreateInfoLabel(string labelText, string valueText, int x, int y)
        {
            var label = new Label
            {
                Text = labelText,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.LightGray,
                Location = new Point(x, y),
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            var value = new Label
            {
                Text = valueText,
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.White,
                Location = new Point(x + 130, y),
                Size = new Size(250, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            return (label, value);
        }

        private string GetEmployeeData(string empId, string field)
        {
            // بيانات تجريبية - في التطبيق الحقيقي ستأتي من قاعدة البيانات
            var employeeData = new Dictionary<string, Dictionary<string, string>>
            {
                ["1001"] = new Dictionary<string, string>
                {
                    ["phone"] = "0501234567", ["email"] = "<EMAIL>", ["nationalId"] = "1234567890",
                    ["birthDate"] = "1990-05-15", ["address"] = "الرياض، حي النرجس", ["department"] = "تقنية المعلومات",
                    ["position"] = "مطور برمجيات أول", ["joinDate"] = "2023-01-15", ["manager"] = "محمد أحمد (مدير التقنية)",
                    ["contractType"] = "دوام كامل", ["basicSalary"] = "12,000 ريال", ["allowances"] = "2,000 ريال",
                    ["insurance"] = "شامل", ["vacation"] = "21 يوم", ["bonus"] = "1,500 ريال", ["netSalary"] = "14,200 ريال",
                    ["attendance"] = "95%", ["lateCount"] = "2 مرات", ["absenceCount"] = "1 يوم", ["overtime"] = "15 ساعة",
                    ["rating"] = "ممتاز (4.8/5)", ["lastReview"] = "2025-04-01"
                },
                ["1002"] = new Dictionary<string, string>
                {
                    ["phone"] = "0507654321", ["email"] = "<EMAIL>", ["nationalId"] = "2345678901",
                    ["birthDate"] = "1988-03-22", ["address"] = "الرياض، حي الملقا", ["department"] = "الموارد البشرية",
                    ["position"] = "مدير موارد بشرية", ["joinDate"] = "2022-11-01", ["manager"] = "فاطمة سالم (المدير العام)",
                    ["contractType"] = "دوام كامل", ["basicSalary"] = "15,000 ريال", ["allowances"] = "2,500 ريال",
                    ["insurance"] = "شامل + عائلة", ["vacation"] = "25 يوم", ["bonus"] = "2,000 ريال", ["netSalary"] = "16,750 ريال",
                    ["attendance"] = "98%", ["lateCount"] = "0 مرات", ["absenceCount"] = "0 يوم", ["overtime"] = "8 ساعات",
                    ["rating"] = "ممتاز (4.9/5)", ["lastReview"] = "2025-03-15"
                },
                ["1003"] = new Dictionary<string, string>
                {
                    ["phone"] = "0509876543", ["email"] = "<EMAIL>", ["nationalId"] = "3456789012",
                    ["birthDate"] = "1985-12-10", ["address"] = "الرياض، حي العليا", ["department"] = "المالية والمحاسبة",
                    ["position"] = "محاسب رئيسي", ["joinDate"] = "2021-03-20", ["manager"] = "عبدالله محمد (مدير المالية)",
                    ["contractType"] = "دوام كامل", ["basicSalary"] = "10,000 ريال", ["allowances"] = "1,500 ريال",
                    ["insurance"] = "شامل", ["vacation"] = "21 يوم", ["bonus"] = "1,200 ريال", ["netSalary"] = "11,400 ريال",
                    ["attendance"] = "92%", ["lateCount"] = "3 مرات", ["absenceCount"] = "2 يوم", ["overtime"] = "12 ساعة",
                    ["rating"] = "جيد جداً (4.2/5)", ["lastReview"] = "2025-02-28"
                }
            };

            if (employeeData.ContainsKey(empId) && employeeData[empId].ContainsKey(field))
            {
                return employeeData[empId][field];
            }

            return "غير محدد";
        }
    }
}
