using System.Linq;
using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class EmployeeFileForm : Form
    {
        private string employeeId;
        private string employeeName;

        public EmployeeFileForm(string empId, string empName)
        {
            employeeId = empId;
            employeeName = empName;
            InitializeComponent();
            InitializeEmployeeFile();
        }

        private void InitializeEmployeeFile()
        {
            this.Text = $"ملف الموظف - {employeeName}";
            this.BackColor = Color.FromArgb(248, 249, 250); // خلفية فاتحة عصرية
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Apply responsive design
            Theme.ResponsiveManager.ApplyResponsiveDesign(this);

            // إنشاء اللوحة الرئيسية
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(30),
                AutoScroll = true,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            // إنشاء شريط العنوان والصورة محسن
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 100,
                BackColor = Color.FromArgb(255, 255, 255), // خلفية بيضاء نظيفة
                Padding = new Padding(30),
                Margin = new Padding(0, 0, 0, 20)
            };

            // زر الرجوع محسن على اليسار
            var backButton = Theme.ModernUIComponents.CreateModernButton("← رجوع",
                Theme.ModernUIComponents.ModernButtonStyle.Primary);
            backButton.Size = new Size(120, 45);
            backButton.Location = new Point(20, 25);
            backButton.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            backButton.Click += (s, e) => GoBackToEmployeeList();

            // عنوان النافذة محسن في أقصى اليمين
            var titleLabel = new Label
            {
                Text = $"📁 ملف الموظف - {employeeName}",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64), // لون داكن للنص
                Dock = DockStyle.Right,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes,
                AutoSize = false,
                Width = 450
            };

            // صورة الموظف على اليسار - أكبر حجماً
            var headerPhotoPanel = new Panel
            {
                Size = new Size(80, 80),
                Dock = DockStyle.Left,
                BackColor = Color.FromArgb(50, 49, 94),
                BorderStyle = BorderStyle.None,
                Margin = new Padding(0, 0, 15, 0)
            };

            // إنشاء صورة ديمو للموظف
            var headerPhotoPictureBox = new PictureBox
            {
                Dock = DockStyle.Fill,
                SizeMode = PictureBoxSizeMode.StretchImage,
                BackColor = Color.FromArgb(70, 130, 180),
                BorderStyle = BorderStyle.FixedSingle
            };

            // إنشاء صورة ديمو احترافية
            var demoBitmap = CreateDemoEmployeePhoto(80, 80);

            headerPhotoPictureBox.Image = demoBitmap;
            headerPhotoPanel.Controls.Add(headerPhotoPictureBox);

            headerPanel.Controls.Add(backButton);
            headerPanel.Controls.Add(titleLabel);
            headerPanel.Controls.Add(headerPhotoPanel);

            // إنشاء لوحة المحتوى محسنة
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(30),
                BackColor = Color.FromArgb(248, 249, 250)
            };

            // إنشاء التخطيط بعمودين محسن (من اليمين لليسار)
            var rightPanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 480,
                Padding = new Padding(15),
                RightToLeft = RightToLeft.Yes,
                BackColor = Color.Transparent
            };

            var leftPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(15),
                RightToLeft = RightToLeft.Yes,
                BackColor = Color.Transparent
            };

            // إنشاء قسم المعلومات الشخصية (العمود الأيمن)
            CreatePersonalInfoSection(rightPanel);

            // إنشاء قسم معلومات العمل (العمود الأيمن)
            CreateWorkInfoSection(rightPanel);

            // إنشاء قسم الراتب والمزايا (العمود الأيسر)
            CreateSalarySection(leftPanel);

            // إنشاء قسم الحضور والأداء (العمود الأيسر)
            CreatePerformanceSection(leftPanel);

            contentPanel.Controls.Add(leftPanel);
            contentPanel.Controls.Add(rightPanel);

            mainPanel.Controls.Add(contentPanel);
            mainPanel.Controls.Add(headerPanel);

            this.Controls.Add(mainPanel);
        }

        private void CreatePersonalInfoSection(Panel parent)
        {
            var personalGroup = CreateRTLGroupBox(
                "المعلومات الشخصية",
                Color.FromArgb(52, 152, 219), // أزرق عصري
                new Size(450, 340),
                new Point(0, 0)
            );

            // المعلومات الأساسية مع حقول أوسع - بدء من الأعلى مباشرة
            var nameInfo = CreateRTLInfoField("الاسم الكامل:", employeeName, 20, 40);
            var idInfo = CreateRTLInfoField("الرقم الوظيفي:", employeeId, 20, 75);
            var phoneInfo = CreateRTLInfoField("رقم الهاتف:", GetEmployeeData(employeeId, "phone"), 20, 110);
            var emailInfo = CreateRTLInfoField("البريد الإلكتروني:", GetEmployeeData(employeeId, "email"), 20, 145);
            var nationalIdInfo = CreateRTLInfoField("رقم الهوية:", GetEmployeeData(employeeId, "nationalId"), 20, 180);
            var birthDateInfo = CreateRTLInfoField("تاريخ الميلاد:", GetEmployeeData(employeeId, "birthDate"), 20, 215);
            var addressInfo = CreateRTLInfoField("العنوان:", GetEmployeeData(employeeId, "address"), 20, 250);

            personalGroup.Controls.AddRange(new Control[] {
                nameInfo.label, nameInfo.textBox,
                idInfo.label, idInfo.textBox,
                phoneInfo.label, phoneInfo.textBox,
                emailInfo.label, emailInfo.textBox,
                nationalIdInfo.label, nationalIdInfo.textBox,
                birthDateInfo.label, birthDateInfo.textBox,
                addressInfo.label, addressInfo.textBox
            });

            parent.Controls.Add(personalGroup);
        }

        private void CreateWorkInfoSection(Panel parent)
        {
            var workGroup = CreateRTLGroupBox(
                "معلومات العمل",
                Color.FromArgb(46, 204, 113), // أخضر عصري
                new Size(450, 240),
                new Point(0, 360) // مسافة أكبر من البطاقة السابقة
            );

            var departmentInfo = CreateRTLInfoField("القسم:", GetEmployeeData(employeeId, "department"), 20, 40);
            var positionInfo = CreateRTLInfoField("المنصب:", GetEmployeeData(employeeId, "position"), 20, 75);
            var joinDateInfo = CreateRTLInfoField("تاريخ التعيين:", GetEmployeeData(employeeId, "joinDate"), 20, 110);
            var managerInfo = CreateRTLInfoField("المدير المباشر:", GetEmployeeData(employeeId, "manager"), 20, 145);
            var contractInfo = CreateRTLInfoField("نوع العقد:", GetEmployeeData(employeeId, "contractType"), 20, 180);

            workGroup.Controls.AddRange(new Control[] {
                departmentInfo.label, departmentInfo.textBox,
                positionInfo.label, positionInfo.textBox,
                joinDateInfo.label, joinDateInfo.textBox,
                managerInfo.label, managerInfo.textBox,
                contractInfo.label, contractInfo.textBox
            });

            parent.Controls.Add(workGroup);
        }

        private void CreateSalarySection(Panel parent)
        {
            var salaryGroup = CreateRTLGroupBox(
                "الراتب والمزايا",
                Color.FromArgb(241, 196, 15), // ذهبي عصري
                new Size(450, 300),
                new Point(0, 0)
            );

            var basicSalaryInfo = CreateRTLInfoField("الراتب الأساسي:", GetEmployeeData(employeeId, "basicSalary"), 20, 40);
            var allowancesInfo = CreateRTLInfoField("البدلات:", GetEmployeeData(employeeId, "allowances"), 20, 75);
            var insuranceInfo = CreateRTLInfoField("التأمين الطبي:", GetEmployeeData(employeeId, "insurance"), 20, 110);
            var vacationInfo = CreateRTLInfoField("الإجازات السنوية:", GetEmployeeData(employeeId, "vacation"), 20, 145);
            var bonusInfo = CreateRTLInfoField("المكافآت:", GetEmployeeData(employeeId, "bonus"), 20, 180);
            var netSalaryInfo = CreateRTLInfoField("صافي الراتب:", GetEmployeeData(employeeId, "netSalary"), 20, 215);

            salaryGroup.Controls.AddRange(new Control[] {
                basicSalaryInfo.label, basicSalaryInfo.textBox,
                allowancesInfo.label, allowancesInfo.textBox,
                insuranceInfo.label, insuranceInfo.textBox,
                vacationInfo.label, vacationInfo.textBox,
                bonusInfo.label, bonusInfo.textBox,
                netSalaryInfo.label, netSalaryInfo.textBox
            });

            parent.Controls.Add(salaryGroup);
        }

        private void CreatePerformanceSection(Panel parent)
        {
            var performanceGroup = CreateRTLGroupBox(
                "الحضور والأداء",
                Color.FromArgb(231, 76, 60), // أحمر عصري
                new Size(450, 270),
                new Point(0, 320) // مسافة أكبر من البطاقة السابقة
            );

            var attendanceInfo = CreateRTLInfoField("معدل الحضور:", GetEmployeeData(employeeId, "attendance"), 20, 40);
            var lateInfo = CreateRTLInfoField("مرات التأخير:", GetEmployeeData(employeeId, "lateCount"), 20, 75);
            var absenceInfo = CreateRTLInfoField("أيام الغياب:", GetEmployeeData(employeeId, "absenceCount"), 20, 110);
            var overtimeInfo = CreateRTLInfoField("ساعات إضافية:", GetEmployeeData(employeeId, "overtime"), 20, 145);
            var ratingInfo = CreateRTLInfoField("تقييم الأداء:", GetEmployeeData(employeeId, "rating"), 20, 180);
            var lastReviewInfo = CreateRTLInfoField("آخر مراجعة:", GetEmployeeData(employeeId, "lastReview"), 20, 215);

            performanceGroup.Controls.AddRange(new Control[] {
                attendanceInfo.label, attendanceInfo.textBox,
                lateInfo.label, lateInfo.textBox,
                absenceInfo.label, absenceInfo.textBox,
                overtimeInfo.label, overtimeInfo.textBox,
                ratingInfo.label, ratingInfo.textBox,
                lastReviewInfo.label, lastReviewInfo.textBox
            });

            parent.Controls.Add(performanceGroup);
        }

        private (Label label, Guna2TextBox textBox) CreateRTLInfoField(string labelText, string valueText, int x, int y)
        {
            var label = new Label
            {
                Text = labelText,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87), // لون رمادي داكن للوضوح
                BackColor = Color.Transparent,
                Location = new Point(x + (int)(210 * Theme.ResponsiveManager.ScaleFactor), y),
                Size = new Size((int)(200 * Theme.ResponsiveManager.ScaleFactor),
                              Theme.ResponsiveManager.Dimensions.InputHeight),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes,
                AutoSize = false
            };

            var textBox = new Guna2TextBox
            {
                Text = valueText,
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.FromArgb(52, 58, 64), // نص داكن
                FillColor = Color.FromArgb(248, 249, 250), // خلفية فاتحة
                BorderColor = Color.FromArgb(206, 212, 218), // حدود رمادية فاتحة
                Location = new Point(x, y),
                Size = new Size((int)(200 * Theme.ResponsiveManager.ScaleFactor),
                              Theme.ResponsiveManager.Dimensions.InputHeight),
                BorderRadius = 8, // زوايا مدورة أقل
                ReadOnly = true,
                RightToLeft = RightToLeft.Yes,
                TextAlign = HorizontalAlignment.Center,
                Cursor = Cursors.Default,
                Multiline = false
            };

            return (label, textBox);
        }

        private (Label label, Label value) CreateInfoLabel(string labelText, string valueText, int x, int y)
        {
            var label = new Label
            {
                Text = labelText,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.LightGray,
                Location = new Point(x, y),
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            var value = new Label
            {
                Text = valueText,
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.White,
                Location = new Point(x + 130, y),
                Size = new Size(250, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            return (label, value);
        }

        private string GetEmployeeData(string empId, string field)
        {
            // بيانات تجريبية - في التطبيق الحقيقي ستأتي من قاعدة البيانات
            var employeeData = new Dictionary<string, Dictionary<string, string>>
            {
                ["1001"] = new Dictionary<string, string>
                {
                    ["phone"] = "0501234567", ["email"] = "<EMAIL>", ["nationalId"] = "1234567890",
                    ["birthDate"] = "1990-05-15", ["address"] = "الرياض، حي النرجس", ["department"] = "تقنية المعلومات",
                    ["position"] = "مطور برمجيات أول", ["joinDate"] = "2023-01-15", ["manager"] = "محمد أحمد (مدير التقنية)",
                    ["contractType"] = "دوام كامل", ["basicSalary"] = "12,000 ريال", ["allowances"] = "2,000 ريال",
                    ["insurance"] = "شامل", ["vacation"] = "21 يوم", ["bonus"] = "1,500 ريال", ["netSalary"] = "14,200 ريال",
                    ["attendance"] = "95%", ["lateCount"] = "2 مرات", ["absenceCount"] = "1 يوم", ["overtime"] = "15 ساعة",
                    ["rating"] = "ممتاز (4.8/5)", ["lastReview"] = "2025-04-01"
                },
                ["1002"] = new Dictionary<string, string>
                {
                    ["phone"] = "0507654321", ["email"] = "<EMAIL>", ["nationalId"] = "2345678901",
                    ["birthDate"] = "1988-03-22", ["address"] = "الرياض، حي الملقا", ["department"] = "الموارد البشرية",
                    ["position"] = "مدير موارد بشرية", ["joinDate"] = "2022-11-01", ["manager"] = "فاطمة سالم (المدير العام)",
                    ["contractType"] = "دوام كامل", ["basicSalary"] = "15,000 ريال", ["allowances"] = "2,500 ريال",
                    ["insurance"] = "شامل + عائلة", ["vacation"] = "25 يوم", ["bonus"] = "2,000 ريال", ["netSalary"] = "16,750 ريال",
                    ["attendance"] = "98%", ["lateCount"] = "0 مرات", ["absenceCount"] = "0 يوم", ["overtime"] = "8 ساعات",
                    ["rating"] = "ممتاز (4.9/5)", ["lastReview"] = "2025-03-15"
                },
                ["1003"] = new Dictionary<string, string>
                {
                    ["phone"] = "0509876543", ["email"] = "<EMAIL>", ["nationalId"] = "3456789012",
                    ["birthDate"] = "1985-12-10", ["address"] = "الرياض، حي العليا", ["department"] = "المالية والمحاسبة",
                    ["position"] = "محاسب رئيسي", ["joinDate"] = "2021-03-20", ["manager"] = "عبدالله محمد (مدير المالية)",
                    ["contractType"] = "دوام كامل", ["basicSalary"] = "10,000 ريال", ["allowances"] = "1,500 ريال",
                    ["insurance"] = "شامل", ["vacation"] = "21 يوم", ["bonus"] = "1,200 ريال", ["netSalary"] = "11,400 ريال",
                    ["attendance"] = "92%", ["lateCount"] = "3 مرات", ["absenceCount"] = "2 يوم", ["overtime"] = "12 ساعة",
                    ["rating"] = "جيد جداً (4.2/5)", ["lastReview"] = "2025-02-28"
                }
            };

            if (employeeData.ContainsKey(empId) && employeeData[empId].ContainsKey(field))
            {
                return employeeData[empId][field];
            }

            return "غير محدد";
        }

        private Bitmap CreateDemoEmployeePhoto(int width, int height)
        {
            var bitmap = new Bitmap(width, height);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

                // خلفية متدرجة احترافية
                using (var brush = new System.Drawing.Drawing2D.LinearGradientBrush(
                    new Rectangle(0, 0, width, height),
                    Color.FromArgb(52, 73, 94),
                    Color.FromArgb(44, 62, 80),
                    System.Drawing.Drawing2D.LinearGradientMode.Vertical))
                {
                    g.FillRectangle(brush, 0, 0, width, height);
                }

                // رسم دائرة للوجه مع ظل
                var faceSize = width * 0.45f;
                var faceX = (width - faceSize) / 2;
                var faceY = height * 0.2f;

                // ظل الوجه
                using (var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
                {
                    g.FillEllipse(shadowBrush, faceX + 2, faceY + 2, faceSize, faceSize);
                }

                // الوجه
                using (var faceBrush = new SolidBrush(Color.FromArgb(255, 220, 177)))
                {
                    g.FillEllipse(faceBrush, faceX, faceY, faceSize, faceSize);
                }

                // العيون
                var eyeSize = faceSize * 0.12f;
                var eyeY = faceY + faceSize * 0.35f;
                using (var eyeBrush = new SolidBrush(Color.FromArgb(70, 70, 70)))
                {
                    g.FillEllipse(eyeBrush, faceX + faceSize * 0.25f, eyeY, eyeSize, eyeSize);
                    g.FillEllipse(eyeBrush, faceX + faceSize * 0.65f, eyeY, eyeSize, eyeSize);
                }

                // الفم
                var mouthWidth = faceSize * 0.3f;
                var mouthX = faceX + (faceSize - mouthWidth) / 2;
                var mouthY = faceY + faceSize * 0.65f;
                using (var mouthPen = new Pen(Color.FromArgb(70, 70, 70), 2))
                {
                    g.DrawArc(mouthPen, mouthX, mouthY, mouthWidth, mouthWidth * 0.5f, 0, 180);
                }

                // الجسم (قميص)
                var bodyWidth = width * 0.6f;
                var bodyHeight = height * 0.4f;
                var bodyX = (width - bodyWidth) / 2;
                var bodyY = height * 0.6f;

                using (var bodyBrush = new SolidBrush(Color.FromArgb(41, 128, 185)))
                {
                    g.FillRectangle(bodyBrush, bodyX, bodyY, bodyWidth, bodyHeight);
                }

                // ياقة القميص
                using (var collarBrush = new SolidBrush(Color.FromArgb(236, 240, 241)))
                {
                    var collarHeight = bodyHeight * 0.3f;
                    g.FillRectangle(collarBrush, bodyX + bodyWidth * 0.3f, bodyY, bodyWidth * 0.4f, collarHeight);
                }

                // إضافة حدود للصورة
                using (var borderPen = new Pen(Color.FromArgb(149, 165, 166), 2))
                {
                    g.DrawRectangle(borderPen, 1, 1, width - 2, height - 2);
                }
            }

            return bitmap;
        }

        private Guna2GroupBox CreateRTLGroupBox(string text, Color borderColor, Size size, Point location)
        {
            var groupBox = new Guna2GroupBox
            {
                Text = "", // إخفاء النص الافتراضي
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64), // لون نص داكن
                FillColor = Color.White, // خلفية بيضاء نظيفة
                CustomBorderColor = borderColor,
                BorderColor = borderColor,
                BorderRadius = 12, // زوايا مدورة عصرية
                Size = size,
                Location = location,
                Padding = new Padding(20), // مسافة أكبر داخلية
                RightToLeft = RightToLeft.Yes,
                // تفعيل الظل للمظهر العصري
            };

            // إضافة Label مخصص للنص على اليمين محسن
            var titleLabel = new Label
            {
                Text = text,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = borderColor, // نفس لون الحدود
                BackColor = Color.Transparent,
                AutoSize = true,
                Location = new Point(groupBox.Width - 180, 8),
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            groupBox.Controls.Add(titleLabel);

            // تحديث موضع التسمية عند تغيير حجم GroupBox
            groupBox.Resize += (sender, e) =>
            {
                var gb = sender as Guna2GroupBox;
                if (gb != null && titleLabel != null)
                {
                    var textWidth = titleLabel.PreferredWidth;
                    titleLabel.Location = new Point(gb.Width - textWidth - 15, 5);
                }
            };

            // تحديث الموضع الأولي
            var initialTextWidth = titleLabel.PreferredWidth;
            titleLabel.Location = new Point(groupBox.Width - initialTextWidth - 15, 5);

            return groupBox;
        }

        private void GoBackToEmployeeList()
        {
            try
            {
                // البحث عن النافذة الرئيسية
                var mainForm = Application.OpenForms.OfType<Form1>().FirstOrDefault();
                if (mainForm != null)
                {
                    // فتح نافذة إدارة الموظفين
                    var employeeManagementForm = new EmployeeManagementForm();
                    mainForm.OpenChildForm(employeeManagementForm, "إدارة الموظفين");
                }
                else
                {
                    // إغلاق النافذة الحالية إذا لم نجد النافذة الرئيسية
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة لقائمة الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
