using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class EmployeeFileForm : Form
    {
        private string employeeId;
        private string employeeName;

        public EmployeeFileForm(string empId, string empName)
        {
            employeeId = empId;
            employeeName = empName;
            InitializeComponent();
            InitializeEmployeeFile();
        }

        private void InitializeEmployeeFile()
        {
            this.Text = $"ملف الموظف - {employeeName}";
            this.Size = new Size(1000, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(34, 33, 74);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إنشاء اللوحة الرئيسية
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                AutoScroll = true
            };

            // إنشاء شريط العنوان والصورة
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(40, 39, 84),
                Padding = new Padding(20)
            };

            // عنوان النافذة في أقصى اليمين
            var titleLabel = new Label
            {
                Text = $"📁 ملف الموظف - {employeeName}",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Dock = DockStyle.Right,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes,
                AutoSize = false,
                Width = 400
            };

            // صورة الموظف على اليسار - أكبر حجماً
            var headerPhotoPanel = new Panel
            {
                Size = new Size(80, 80),
                Dock = DockStyle.Left,
                BackColor = Color.FromArgb(50, 49, 94),
                BorderStyle = BorderStyle.None,
                Margin = new Padding(0, 0, 15, 0)
            };

            // إنشاء صورة ديمو للموظف
            var headerPhotoPictureBox = new PictureBox
            {
                Dock = DockStyle.Fill,
                SizeMode = PictureBoxSizeMode.StretchImage,
                BackColor = Color.FromArgb(70, 130, 180),
                BorderStyle = BorderStyle.FixedSingle
            };

            // إنشاء صورة ديمو احترافية
            var demoBitmap = CreateDemoEmployeePhoto(80, 80);

            headerPhotoPictureBox.Image = demoBitmap;
            headerPhotoPanel.Controls.Add(headerPhotoPictureBox);

            headerPanel.Controls.Add(titleLabel);
            headerPanel.Controls.Add(headerPhotoPanel);

            // إنشاء لوحة المحتوى
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // إنشاء التخطيط بعمودين (من اليمين لليسار)
            var rightPanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 450,
                Padding = new Padding(10),
                RightToLeft = RightToLeft.Yes
            };

            var leftPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                RightToLeft = RightToLeft.Yes
            };

            // إنشاء قسم المعلومات الشخصية (العمود الأيمن)
            CreatePersonalInfoSection(rightPanel);

            // إنشاء قسم معلومات العمل (العمود الأيمن)
            CreateWorkInfoSection(rightPanel);

            // إنشاء قسم الراتب والمزايا (العمود الأيسر)
            CreateSalarySection(leftPanel);

            // إنشاء قسم الحضور والأداء (العمود الأيسر)
            CreatePerformanceSection(leftPanel);

            contentPanel.Controls.Add(leftPanel);
            contentPanel.Controls.Add(rightPanel);

            mainPanel.Controls.Add(contentPanel);
            mainPanel.Controls.Add(headerPanel);

            this.Controls.Add(mainPanel);
        }

        private void CreatePersonalInfoSection(Panel parent)
        {
            var personalGroup = CreateRTLGroupBox(
                "المعلومات الشخصية",
                Color.FromArgb(0, 126, 249),
                new Size(420, 320),
                new Point(10, 10)
            );

            // المعلومات الأساسية مع حقول أوسع - بدء من الأعلى مباشرة
            var nameInfo = CreateRTLInfoField("الاسم الكامل:", employeeName, 20, 40);
            var idInfo = CreateRTLInfoField("الرقم الوظيفي:", employeeId, 20, 75);
            var phoneInfo = CreateRTLInfoField("رقم الهاتف:", GetEmployeeData(employeeId, "phone"), 20, 110);
            var emailInfo = CreateRTLInfoField("البريد الإلكتروني:", GetEmployeeData(employeeId, "email"), 20, 145);
            var nationalIdInfo = CreateRTLInfoField("رقم الهوية:", GetEmployeeData(employeeId, "nationalId"), 20, 180);
            var birthDateInfo = CreateRTLInfoField("تاريخ الميلاد:", GetEmployeeData(employeeId, "birthDate"), 20, 215);
            var addressInfo = CreateRTLInfoField("العنوان:", GetEmployeeData(employeeId, "address"), 20, 250);

            personalGroup.Controls.AddRange(new Control[] {
                nameInfo.textBox, nameInfo.label,
                idInfo.textBox, idInfo.label,
                phoneInfo.textBox, phoneInfo.label,
                emailInfo.textBox, emailInfo.label,
                nationalIdInfo.textBox, nationalIdInfo.label,
                birthDateInfo.textBox, birthDateInfo.label,
                addressInfo.textBox, addressInfo.label
            });

            parent.Controls.Add(personalGroup);
        }

        private void CreateWorkInfoSection(Panel parent)
        {
            var workGroup = CreateRTLGroupBox(
                "معلومات العمل",
                Color.FromArgb(40, 167, 69),
                new Size(420, 220),
                new Point(10, 340)
            );

            var departmentInfo = CreateRTLInfoField("القسم:", GetEmployeeData(employeeId, "department"), 20, 40);
            var positionInfo = CreateRTLInfoField("المنصب:", GetEmployeeData(employeeId, "position"), 20, 75);
            var joinDateInfo = CreateRTLInfoField("تاريخ التعيين:", GetEmployeeData(employeeId, "joinDate"), 20, 110);
            var managerInfo = CreateRTLInfoField("المدير المباشر:", GetEmployeeData(employeeId, "manager"), 20, 145);
            var contractInfo = CreateRTLInfoField("نوع العقد:", GetEmployeeData(employeeId, "contractType"), 20, 180);

            workGroup.Controls.AddRange(new Control[] {
                departmentInfo.textBox, departmentInfo.label,
                positionInfo.textBox, positionInfo.label,
                joinDateInfo.textBox, joinDateInfo.label,
                managerInfo.textBox, managerInfo.label,
                contractInfo.textBox, contractInfo.label
            });

            parent.Controls.Add(workGroup);
        }

        private void CreateSalarySection(Panel parent)
        {
            var salaryGroup = CreateRTLGroupBox(
                "الراتب والمزايا",
                Color.FromArgb(255, 193, 7),
                new Size(420, 280),
                new Point(10, 10)
            );

            var basicSalaryInfo = CreateRTLInfoField("الراتب الأساسي:", GetEmployeeData(employeeId, "basicSalary"), 20, 40);
            var allowancesInfo = CreateRTLInfoField("البدلات:", GetEmployeeData(employeeId, "allowances"), 20, 75);
            var insuranceInfo = CreateRTLInfoField("التأمين الطبي:", GetEmployeeData(employeeId, "insurance"), 20, 110);
            var vacationInfo = CreateRTLInfoField("الإجازات السنوية:", GetEmployeeData(employeeId, "vacation"), 20, 145);
            var bonusInfo = CreateRTLInfoField("المكافآت:", GetEmployeeData(employeeId, "bonus"), 20, 180);
            var netSalaryInfo = CreateRTLInfoField("صافي الراتب:", GetEmployeeData(employeeId, "netSalary"), 20, 215);

            salaryGroup.Controls.AddRange(new Control[] {
                basicSalaryInfo.textBox, basicSalaryInfo.label,
                allowancesInfo.textBox, allowancesInfo.label,
                insuranceInfo.textBox, insuranceInfo.label,
                vacationInfo.textBox, vacationInfo.label,
                bonusInfo.textBox, bonusInfo.label,
                netSalaryInfo.textBox, netSalaryInfo.label
            });

            parent.Controls.Add(salaryGroup);
        }

        private void CreatePerformanceSection(Panel parent)
        {
            var performanceGroup = CreateRTLGroupBox(
                "الحضور والأداء",
                Color.FromArgb(220, 53, 69),
                new Size(420, 250),
                new Point(10, 300)
            );

            var attendanceInfo = CreateRTLInfoField("معدل الحضور:", GetEmployeeData(employeeId, "attendance"), 20, 40);
            var lateInfo = CreateRTLInfoField("مرات التأخير:", GetEmployeeData(employeeId, "lateCount"), 20, 75);
            var absenceInfo = CreateRTLInfoField("أيام الغياب:", GetEmployeeData(employeeId, "absenceCount"), 20, 110);
            var overtimeInfo = CreateRTLInfoField("ساعات إضافية:", GetEmployeeData(employeeId, "overtime"), 20, 145);
            var ratingInfo = CreateRTLInfoField("تقييم الأداء:", GetEmployeeData(employeeId, "rating"), 20, 180);
            var lastReviewInfo = CreateRTLInfoField("آخر مراجعة:", GetEmployeeData(employeeId, "lastReview"), 20, 215);

            performanceGroup.Controls.AddRange(new Control[] {
                attendanceInfo.label, attendanceInfo.textBox,
                lateInfo.label, lateInfo.textBox,
                absenceInfo.label, absenceInfo.textBox,
                overtimeInfo.label, overtimeInfo.textBox,
                ratingInfo.label, ratingInfo.textBox,
                lastReviewInfo.label, lastReviewInfo.textBox
            });

            parent.Controls.Add(performanceGroup);
        }

        private (Label label, Guna2TextBox textBox) CreateRTLInfoField(string labelText, string valueText, int x, int y)
        {
            var label = new Label
            {
                Text = labelText,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(200, 200, 200), // لون رمادي أفتح للوضوح
                BackColor = Color.Transparent,
                Location = new Point(x, y),
                Size = new Size(180, 25),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes,
                AutoSize = false
            };

            var textBox = new Guna2TextBox
            {
                Text = valueText,
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.White,
                FillColor = Color.FromArgb(45, 44, 84), // لون أفتح قليلاً من الخلفية
                BorderColor = Color.FromArgb(80, 79, 124),
                Location = new Point(x + 190, y),
                Size = new Size(190, 30),
                BorderRadius = 5,
                ReadOnly = true,
                RightToLeft = RightToLeft.Yes,
                TextAlign = HorizontalAlignment.Right,
                Cursor = Cursors.Default,
                Multiline = false
            };

            return (label, textBox);
        }

        private (Label label, Label value) CreateInfoLabel(string labelText, string valueText, int x, int y)
        {
            var label = new Label
            {
                Text = labelText,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.LightGray,
                Location = new Point(x, y),
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            var value = new Label
            {
                Text = valueText,
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.White,
                Location = new Point(x + 130, y),
                Size = new Size(250, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            return (label, value);
        }

        private string GetEmployeeData(string empId, string field)
        {
            // بيانات تجريبية - في التطبيق الحقيقي ستأتي من قاعدة البيانات
            var employeeData = new Dictionary<string, Dictionary<string, string>>
            {
                ["1001"] = new Dictionary<string, string>
                {
                    ["phone"] = "0501234567", ["email"] = "<EMAIL>", ["nationalId"] = "1234567890",
                    ["birthDate"] = "1990-05-15", ["address"] = "الرياض، حي النرجس", ["department"] = "تقنية المعلومات",
                    ["position"] = "مطور برمجيات أول", ["joinDate"] = "2023-01-15", ["manager"] = "محمد أحمد (مدير التقنية)",
                    ["contractType"] = "دوام كامل", ["basicSalary"] = "12,000 ريال", ["allowances"] = "2,000 ريال",
                    ["insurance"] = "شامل", ["vacation"] = "21 يوم", ["bonus"] = "1,500 ريال", ["netSalary"] = "14,200 ريال",
                    ["attendance"] = "95%", ["lateCount"] = "2 مرات", ["absenceCount"] = "1 يوم", ["overtime"] = "15 ساعة",
                    ["rating"] = "ممتاز (4.8/5)", ["lastReview"] = "2025-04-01"
                },
                ["1002"] = new Dictionary<string, string>
                {
                    ["phone"] = "0507654321", ["email"] = "<EMAIL>", ["nationalId"] = "2345678901",
                    ["birthDate"] = "1988-03-22", ["address"] = "الرياض، حي الملقا", ["department"] = "الموارد البشرية",
                    ["position"] = "مدير موارد بشرية", ["joinDate"] = "2022-11-01", ["manager"] = "فاطمة سالم (المدير العام)",
                    ["contractType"] = "دوام كامل", ["basicSalary"] = "15,000 ريال", ["allowances"] = "2,500 ريال",
                    ["insurance"] = "شامل + عائلة", ["vacation"] = "25 يوم", ["bonus"] = "2,000 ريال", ["netSalary"] = "16,750 ريال",
                    ["attendance"] = "98%", ["lateCount"] = "0 مرات", ["absenceCount"] = "0 يوم", ["overtime"] = "8 ساعات",
                    ["rating"] = "ممتاز (4.9/5)", ["lastReview"] = "2025-03-15"
                },
                ["1003"] = new Dictionary<string, string>
                {
                    ["phone"] = "0509876543", ["email"] = "<EMAIL>", ["nationalId"] = "3456789012",
                    ["birthDate"] = "1985-12-10", ["address"] = "الرياض، حي العليا", ["department"] = "المالية والمحاسبة",
                    ["position"] = "محاسب رئيسي", ["joinDate"] = "2021-03-20", ["manager"] = "عبدالله محمد (مدير المالية)",
                    ["contractType"] = "دوام كامل", ["basicSalary"] = "10,000 ريال", ["allowances"] = "1,500 ريال",
                    ["insurance"] = "شامل", ["vacation"] = "21 يوم", ["bonus"] = "1,200 ريال", ["netSalary"] = "11,400 ريال",
                    ["attendance"] = "92%", ["lateCount"] = "3 مرات", ["absenceCount"] = "2 يوم", ["overtime"] = "12 ساعة",
                    ["rating"] = "جيد جداً (4.2/5)", ["lastReview"] = "2025-02-28"
                }
            };

            if (employeeData.ContainsKey(empId) && employeeData[empId].ContainsKey(field))
            {
                return employeeData[empId][field];
            }

            return "غير محدد";
        }

        private Bitmap CreateDemoEmployeePhoto(int width, int height)
        {
            var bitmap = new Bitmap(width, height);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

                // خلفية متدرجة احترافية
                using (var brush = new System.Drawing.Drawing2D.LinearGradientBrush(
                    new Rectangle(0, 0, width, height),
                    Color.FromArgb(52, 73, 94),
                    Color.FromArgb(44, 62, 80),
                    System.Drawing.Drawing2D.LinearGradientMode.Vertical))
                {
                    g.FillRectangle(brush, 0, 0, width, height);
                }

                // رسم دائرة للوجه مع ظل
                var faceSize = width * 0.45f;
                var faceX = (width - faceSize) / 2;
                var faceY = height * 0.2f;

                // ظل الوجه
                using (var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
                {
                    g.FillEllipse(shadowBrush, faceX + 2, faceY + 2, faceSize, faceSize);
                }

                // الوجه
                using (var faceBrush = new SolidBrush(Color.FromArgb(255, 220, 177)))
                {
                    g.FillEllipse(faceBrush, faceX, faceY, faceSize, faceSize);
                }

                // العيون
                var eyeSize = faceSize * 0.12f;
                var eyeY = faceY + faceSize * 0.35f;
                using (var eyeBrush = new SolidBrush(Color.FromArgb(70, 70, 70)))
                {
                    g.FillEllipse(eyeBrush, faceX + faceSize * 0.25f, eyeY, eyeSize, eyeSize);
                    g.FillEllipse(eyeBrush, faceX + faceSize * 0.65f, eyeY, eyeSize, eyeSize);
                }

                // الفم
                var mouthWidth = faceSize * 0.3f;
                var mouthX = faceX + (faceSize - mouthWidth) / 2;
                var mouthY = faceY + faceSize * 0.65f;
                using (var mouthPen = new Pen(Color.FromArgb(70, 70, 70), 2))
                {
                    g.DrawArc(mouthPen, mouthX, mouthY, mouthWidth, mouthWidth * 0.5f, 0, 180);
                }

                // الجسم (قميص)
                var bodyWidth = width * 0.6f;
                var bodyHeight = height * 0.4f;
                var bodyX = (width - bodyWidth) / 2;
                var bodyY = height * 0.6f;

                using (var bodyBrush = new SolidBrush(Color.FromArgb(41, 128, 185)))
                {
                    g.FillRectangle(bodyBrush, bodyX, bodyY, bodyWidth, bodyHeight);
                }

                // ياقة القميص
                using (var collarBrush = new SolidBrush(Color.FromArgb(236, 240, 241)))
                {
                    var collarHeight = bodyHeight * 0.3f;
                    g.FillRectangle(collarBrush, bodyX + bodyWidth * 0.3f, bodyY, bodyWidth * 0.4f, collarHeight);
                }

                // إضافة حدود للصورة
                using (var borderPen = new Pen(Color.FromArgb(149, 165, 166), 2))
                {
                    g.DrawRectangle(borderPen, 1, 1, width - 2, height - 2);
                }
            }

            return bitmap;
        }

        private Guna2GroupBox CreateRTLGroupBox(string text, Color borderColor, Size size, Point location)
        {
            var groupBox = new Guna2GroupBox
            {
                Text = "", // إخفاء النص الافتراضي
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                FillColor = Color.FromArgb(34, 33, 74), // نفس لون خلفية النافذة
                CustomBorderColor = borderColor,
                BorderColor = borderColor,
                Size = size,
                Location = location,
                Padding = new Padding(15),
                RightToLeft = RightToLeft.Yes
            };

            // إضافة Label مخصص للنص على اليمين
            var titleLabel = new Label
            {
                Text = text,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                AutoSize = true,
                Location = new Point(groupBox.Width - 150, 5),
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            groupBox.Controls.Add(titleLabel);

            // تحديث موضع التسمية عند تغيير حجم GroupBox
            groupBox.Resize += (sender, e) =>
            {
                var gb = sender as Guna2GroupBox;
                if (gb != null && titleLabel != null)
                {
                    var textWidth = titleLabel.PreferredWidth;
                    titleLabel.Location = new Point(gb.Width - textWidth - 15, 5);
                }
            };

            // تحديث الموضع الأولي
            var initialTextWidth = titleLabel.PreferredWidth;
            titleLabel.Location = new Point(groupBox.Width - initialTextWidth - 15, 5);

            return groupBox;
        }
    }
}
