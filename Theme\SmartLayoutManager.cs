using System;
using System.Drawing;
using System.Windows.Forms;
using System.Linq;

namespace ModernWinApp.Theme
{
    public static class SmartLayoutManager
    {
        // Layout configurations
        public enum LayoutMode
        {
            Centered,           // توسيط العناصر
            Distributed,        // توزيع متساوي
            Responsive,         // تخطيط متجاوب
            Grid,              // شبكة منتظمة
            Adaptive           // تكيفي حسب المحتوى
        }

        public static LayoutMode CurrentMode { get; set; } = LayoutMode.Responsive;

        // Margins and spacing
        public static class Spacing
        {
            public static int ContainerMargin => (int)(20 * ResponsiveManager.ScaleFactor);
            public static int ElementSpacing => (int)(15 * ResponsiveManager.ScaleFactor);
            public static int SectionSpacing => (int)(30 * ResponsiveManager.ScaleFactor);
            public static int GridSpacing => (int)(20 * ResponsiveManager.ScaleFactor);
        }

        // Apply smart layout to a container
        public static void ApplySmartLayout(Control container, LayoutMode mode = LayoutMode.Responsive)
        {
            if (container == null) return;

            CurrentMode = mode;

            // Calculate available space
            var availableArea = GetAvailableArea(container);

            // Apply layout based on mode
            switch (mode)
            {
                case LayoutMode.Centered:
                    ApplyCenteredLayout(container, availableArea);
                    break;
                case LayoutMode.Distributed:
                    ApplyDistributedLayout(container, availableArea);
                    break;
                case LayoutMode.Responsive:
                    ApplyResponsiveLayout(container, availableArea);
                    break;
                case LayoutMode.Grid:
                    ApplyGridLayout(container, availableArea);
                    break;
                case LayoutMode.Adaptive:
                    ApplyAdaptiveLayout(container, availableArea);
                    break;
            }
        }

        private static Rectangle GetAvailableArea(Control container)
        {
            var margin = Spacing.ContainerMargin;
            return new Rectangle(
                margin,
                margin,
                container.Width - (margin * 2),
                container.Height - (margin * 2)
            );
        }

        // Centered Layout - توسيط العناصر
        private static void ApplyCenteredLayout(Control container, Rectangle availableArea)
        {
            var controls = GetLayoutableControls(container);
            if (!controls.Any()) return;

            // Calculate total content size
            var totalWidth = controls.Sum(c => c.Width) + (controls.Length - 1) * Spacing.ElementSpacing;
            var maxHeight = controls.Max(c => c.Height);

            // Center horizontally
            var startX = availableArea.X + (availableArea.Width - totalWidth) / 2;
            var startY = availableArea.Y + (availableArea.Height - maxHeight) / 2;

            var currentX = startX;
            foreach (var control in controls)
            {
                control.Location = new Point(currentX, startY + (maxHeight - control.Height) / 2);
                currentX += control.Width + Spacing.ElementSpacing;
            }
        }

        // Distributed Layout - توزيع متساوي
        private static void ApplyDistributedLayout(Control container, Rectangle availableArea)
        {
            var controls = GetLayoutableControls(container);
            if (!controls.Any()) return;

            var totalControlsWidth = controls.Sum(c => c.Width);
            var availableSpacing = availableArea.Width - totalControlsWidth;
            var spacing = controls.Length > 1 ? availableSpacing / (controls.Length + 1) : availableSpacing / 2;

            var currentX = availableArea.X + spacing;
            var centerY = availableArea.Y + availableArea.Height / 2;

            foreach (var control in controls)
            {
                control.Location = new Point(currentX, centerY - control.Height / 2);
                currentX += control.Width + spacing;
            }
        }

        // Responsive Layout - تخطيط متجاوب
        private static void ApplyResponsiveLayout(Control container, Rectangle availableArea)
        {
            var controls = GetLayoutableControls(container);
            if (!controls.Any()) return;

            // Determine layout based on available space and control count
            if (availableArea.Width > 1200 && controls.Length > 3)
            {
                ApplyMultiColumnLayout(controls, availableArea, 3);
            }
            else if (availableArea.Width > 800 && controls.Length > 2)
            {
                ApplyMultiColumnLayout(controls, availableArea, 2);
            }
            else
            {
                ApplySingleColumnLayout(controls, availableArea);
            }
        }

        // Grid Layout - شبكة منتظمة
        private static void ApplyGridLayout(Control container, Rectangle availableArea)
        {
            var controls = GetLayoutableControls(container);
            if (!controls.Any()) return;

            // Calculate optimal grid dimensions
            var columns = CalculateOptimalColumns(controls.Length, availableArea.Width);
            var rows = (int)Math.Ceiling((double)controls.Length / columns);

            var cellWidth = (availableArea.Width - (columns - 1) * Spacing.GridSpacing) / columns;
            var cellHeight = (availableArea.Height - (rows - 1) * Spacing.GridSpacing) / rows;

            for (int i = 0; i < controls.Length; i++)
            {
                var row = i / columns;
                var col = i % columns;

                var x = availableArea.X + col * (cellWidth + Spacing.GridSpacing);
                var y = availableArea.Y + row * (cellHeight + Spacing.GridSpacing);

                var control = controls[i];

                // Center control within cell
                var centerX = x + (cellWidth - control.Width) / 2;
                var centerY = y + (cellHeight - control.Height) / 2;

                control.Location = new Point(Math.Max(x, centerX), Math.Max(y, centerY));
            }
        }

        // Adaptive Layout - تكيفي حسب المحتوى
        private static void ApplyAdaptiveLayout(Control container, Rectangle availableArea)
        {
            var controls = GetLayoutableControls(container);
            if (!controls.Any()) return;

            // Group controls by type/size
            var largeControls = controls.Where(c => c.Width > availableArea.Width * 0.6).ToList();
            var mediumControls = controls.Where(c => c.Width > availableArea.Width * 0.3 && c.Width <= availableArea.Width * 0.6).ToList();
            var smallControls = controls.Where(c => c.Width <= availableArea.Width * 0.3).ToList();

            var currentY = availableArea.Y;

            // Layout large controls (full width)
            foreach (var control in largeControls)
            {
                var centerX = availableArea.X + (availableArea.Width - control.Width) / 2;
                control.Location = new Point(centerX, currentY);
                currentY += control.Height + Spacing.SectionSpacing;
            }

            // Layout medium controls (2 per row if possible)
            for (int i = 0; i < mediumControls.Count; i += 2)
            {
                if (i + 1 < mediumControls.Count)
                {
                    // Two controls per row
                    var control1 = mediumControls[i];
                    var control2 = mediumControls[i + 1];

                    var totalWidth = control1.Width + control2.Width + Spacing.ElementSpacing;
                    var startX = availableArea.X + (availableArea.Width - totalWidth) / 2;

                    control1.Location = new Point(startX, currentY);
                    control2.Location = new Point(startX + control1.Width + Spacing.ElementSpacing, currentY);

                    currentY += Math.Max(control1.Height, control2.Height) + Spacing.SectionSpacing;
                }
                else
                {
                    // Single control centered
                    var control = mediumControls[i];
                    var centerX = availableArea.X + (availableArea.Width - control.Width) / 2;
                    control.Location = new Point(centerX, currentY);
                    currentY += control.Height + Spacing.SectionSpacing;
                }
            }

            // Layout small controls (3-4 per row)
            var smallControlsPerRow = availableArea.Width > 1000 ? 4 : 3;
            for (int i = 0; i < smallControls.Count; i += smallControlsPerRow)
            {
                var rowControls = smallControls.Skip(i).Take(smallControlsPerRow).ToList();
                var totalWidth = rowControls.Sum(c => c.Width) + (rowControls.Count - 1) * Spacing.ElementSpacing;
                var startX = availableArea.X + (availableArea.Width - totalWidth) / 2;

                var currentX = startX;
                var maxHeight = rowControls.Max(c => c.Height);

                foreach (var control in rowControls)
                {
                    control.Location = new Point(currentX, currentY + (maxHeight - control.Height) / 2);
                    currentX += control.Width + Spacing.ElementSpacing;
                }

                currentY += maxHeight + Spacing.SectionSpacing;
            }
        }

        // Helper methods
        private static void ApplyMultiColumnLayout(Control[] controls, Rectangle availableArea, int columns)
        {
            var columnWidth = (availableArea.Width - (columns - 1) * Spacing.ElementSpacing) / columns;
            var controlsPerColumn = (int)Math.Ceiling((double)controls.Length / columns);

            for (int col = 0; col < columns; col++)
            {
                var columnX = availableArea.X + col * (columnWidth + Spacing.ElementSpacing);
                var currentY = availableArea.Y;

                var startIndex = col * controlsPerColumn;
                var endIndex = Math.Min(startIndex + controlsPerColumn, controls.Length);

                for (int i = startIndex; i < endIndex; i++)
                {
                    var control = controls[i];
                    var centerX = columnX + (columnWidth - control.Width) / 2;
                    control.Location = new Point(Math.Max(columnX, centerX), currentY);
                    currentY += control.Height + Spacing.ElementSpacing;
                }
            }
        }

        private static void ApplySingleColumnLayout(Control[] controls, Rectangle availableArea)
        {
            var currentY = availableArea.Y;

            foreach (var control in controls)
            {
                var centerX = availableArea.X + (availableArea.Width - control.Width) / 2;
                control.Location = new Point(centerX, currentY);
                currentY += control.Height + Spacing.ElementSpacing;
            }
        }

        private static Control[] GetLayoutableControls(Control container)
        {
            return container.Controls
                .Cast<Control>()
                .Where(c => c.Visible && c.Width > 0 && c.Height > 0)
                .OrderBy(c => c.TabIndex)
                .ToArray();
        }

        private static int CalculateOptimalColumns(int controlCount, int availableWidth)
        {
            if (availableWidth > 1400) return Math.Min(4, controlCount);
            if (availableWidth > 1000) return Math.Min(3, controlCount);
            if (availableWidth > 600) return Math.Min(2, controlCount);
            return 1;
        }

        // Auto-layout for DataGridView
        public static void ApplyDataGridLayout(DataGridView grid, Control container)
        {
            if (grid == null || container == null) return;

            var margin = Spacing.ContainerMargin;
            var availableWidth = container.Width - (margin * 2);
            var availableHeight = container.Height - (margin * 2);

            // Center the grid with optimal size
            var optimalWidth = Math.Min(availableWidth, (int)(availableWidth * 0.95));
            var optimalHeight = Math.Min(availableHeight, (int)(availableHeight * 0.9));

            var centerX = (container.Width - optimalWidth) / 2;
            var centerY = (container.Height - optimalHeight) / 2;

            grid.Location = new Point(centerX, centerY);
            grid.Size = new Size(optimalWidth, optimalHeight);
        }

        // Auto-layout for forms
        public static void ApplyFormLayout(Form form)
        {
            if (form == null) return;

            form.SuspendLayout();
            try
            {
                ApplySmartLayout(form, CurrentMode);
            }
            finally
            {
                form.ResumeLayout(true);
            }
        }
    }
}
