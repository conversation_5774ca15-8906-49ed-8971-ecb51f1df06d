using System;
using System.Drawing;
using System.Windows.Forms;
using System.Linq;

namespace ModernWinApp.Theme
{
    public static class SmartLayoutManager
    {
        // Layout configurations
        public enum LayoutMode
        {
            Centered,           // توسيط العناصر
            Distributed,        // توزيع متساوي
            Responsive,         // تخطيط متجاوب
            Grid,              // شبكة منتظمة
            Adaptive           // تكيفي حسب المحتوى
        }

        public static LayoutMode CurrentMode { get; set; } = LayoutMode.Responsive;

        // Margins and spacing
        public static class Spacing
        {
            public static int ContainerMargin => (int)(30 * ResponsiveManager.ScaleFactor);
            public static int ElementSpacing => (int)(25 * ResponsiveManager.ScaleFactor);
            public static int SectionSpacing => (int)(40 * ResponsiveManager.ScaleFactor);
            public static int GridSpacing => (int)(30 * ResponsiveManager.ScaleFactor);
            public static int CardSpacing => (int)(35 * ResponsiveManager.ScaleFactor);
            public static int RowSpacing => (int)(45 * ResponsiveManager.ScaleFactor);
            public static int GroupSpacing => (int)(50 * ResponsiveManager.ScaleFactor);
        }

        // Apply smart layout to a container
        public static void ApplySmartLayout(Control container, LayoutMode mode = LayoutMode.Responsive)
        {
            if (container == null || container.Width <= 0 || container.Height <= 0) return;

            try
            {
                CurrentMode = mode;

                // Calculate available space
                var availableArea = GetAvailableArea(container);

                // Ensure we have valid area
                if (availableArea.Width <= 0 || availableArea.Height <= 0) return;

                // Apply layout based on mode
                switch (mode)
                {
                    case LayoutMode.Centered:
                        ApplyCenteredLayout(container, availableArea);
                        break;
                    case LayoutMode.Distributed:
                        ApplyDistributedLayout(container, availableArea);
                        break;
                    case LayoutMode.Responsive:
                        ApplyResponsiveLayout(container, availableArea);
                        break;
                    case LayoutMode.Grid:
                        ApplyGridLayout(container, availableArea);
                        break;
                    case LayoutMode.Adaptive:
                        ApplyAdaptiveLayout(container, availableArea);
                        break;
                }
            }
            catch (Exception ex)
            {
                // Log error but don't crash
                System.Diagnostics.Debug.WriteLine($"SmartLayoutManager error: {ex.Message}");
            }
        }

        private static Rectangle GetAvailableArea(Control container)
        {
            var margin = Spacing.ContainerMargin;
            return new Rectangle(
                margin,
                margin,
                container.Width - (margin * 2),
                container.Height - (margin * 2)
            );
        }

        // Centered Layout - توسيط العناصر
        private static void ApplyCenteredLayout(Control container, Rectangle availableArea)
        {
            var controls = GetLayoutableControls(container);
            if (!controls.Any()) return;

            // Calculate total content size
            var totalWidth = controls.Sum(c => c.Width) + (controls.Length - 1) * Spacing.ElementSpacing;
            var maxHeight = controls.Max(c => c.Height);

            // Center horizontally
            var startX = availableArea.X + (availableArea.Width - totalWidth) / 2;
            var startY = availableArea.Y + (availableArea.Height - maxHeight) / 2;

            var currentX = startX;
            foreach (var control in controls)
            {
                control.Location = new Point(currentX, startY + (maxHeight - control.Height) / 2);
                currentX += control.Width + Spacing.ElementSpacing;
            }
        }

        // Distributed Layout - توزيع متساوي
        private static void ApplyDistributedLayout(Control container, Rectangle availableArea)
        {
            var controls = GetLayoutableControls(container);
            if (!controls.Any()) return;

            // التحقق من اتجاه التخطيط (RTL أم LTR)
            bool isRTL = container.RightToLeft == RightToLeft.Yes;

            var totalControlsWidth = controls.Sum(c => c.Width);
            var availableSpacing = availableArea.Width - totalControlsWidth;

            // حساب المسافات بشكل أفضل للتوزيع المتساوي
            var spacing = controls.Length > 1 ? availableSpacing / (controls.Length + 1) : availableSpacing / 2;

            // ضمان حد أدنى للمسافات
            spacing = Math.Max(spacing, 15);

            var centerY = availableArea.Y + availableArea.Height / 2;

            if (isRTL)
            {
                // للتطبيقات العربية: البدء من اليمين
                var currentX = availableArea.X + availableArea.Width - spacing;

                foreach (var control in controls)
                {
                    currentX -= control.Width;

                    // توسيط عمودي للعنصر
                    var y = centerY - control.Height / 2;
                    y = Math.Max(availableArea.Y, Math.Min(y, availableArea.Y + availableArea.Height - control.Height));

                    control.Location = new Point(Math.Max(availableArea.X, currentX), y);
                    currentX -= spacing;
                }
            }
            else
            {
                // للتطبيقات الإنجليزية: البدء من اليسار
                var currentX = availableArea.X + spacing;

                foreach (var control in controls)
                {
                    // توسيط عمودي للعنصر
                    var y = centerY - control.Height / 2;
                    y = Math.Max(availableArea.Y, Math.Min(y, availableArea.Y + availableArea.Height - control.Height));

                    control.Location = new Point(currentX, y);
                    currentX += control.Width + spacing;
                }
            }
        }

        // Responsive Layout - تخطيط متجاوب
        private static void ApplyResponsiveLayout(Control container, Rectangle availableArea)
        {
            var controls = GetLayoutableControls(container);
            if (!controls.Any()) return;

            // Determine layout based on available space and control count
            if (availableArea.Width > 1200 && controls.Length > 3)
            {
                ApplyMultiColumnLayout(controls, availableArea, 3);
            }
            else if (availableArea.Width > 800 && controls.Length > 2)
            {
                ApplyMultiColumnLayout(controls, availableArea, 2);
            }
            else
            {
                ApplySingleColumnLayout(controls, availableArea);
            }
        }

        // Grid Layout - شبكة منتظمة
        private static void ApplyGridLayout(Control container, Rectangle availableArea)
        {
            var controls = GetLayoutableControls(container);
            if (!controls.Any()) return;

            // Calculate optimal grid dimensions
            var columns = CalculateOptimalColumns(controls.Length, availableArea.Width);
            var rows = (int)Math.Ceiling((double)controls.Length / columns);

            var cellWidth = (availableArea.Width - (columns - 1) * Spacing.GridSpacing) / columns;
            var cellHeight = (availableArea.Height - (rows - 1) * Spacing.GridSpacing) / rows;

            for (int i = 0; i < controls.Length; i++)
            {
                var row = i / columns;
                var col = i % columns;

                var x = availableArea.X + col * (cellWidth + Spacing.GridSpacing);
                var y = availableArea.Y + row * (cellHeight + Spacing.GridSpacing);

                var control = controls[i];

                // Center control within cell
                var centerX = x + (cellWidth - control.Width) / 2;
                var centerY = y + (cellHeight - control.Height) / 2;

                control.Location = new Point(Math.Max(x, centerX), Math.Max(y, centerY));
            }
        }

        // Adaptive Layout - تكيفي حسب المحتوى
        private static void ApplyAdaptiveLayout(Control container, Rectangle availableArea)
        {
            var controls = GetLayoutableControls(container);
            if (!controls.Any()) return;

            // Calculate optimal layout based on available space
            var cardsPerRow = CalculateCardsPerRow(availableArea.Width);
            var totalRows = (int)Math.Ceiling((double)controls.Length / cardsPerRow);

            // Calculate card dimensions and spacing
            var cardSpacing = Spacing.CardSpacing;
            var rowSpacing = Spacing.RowSpacing;

            // Calculate available width for cards
            var totalSpacingWidth = (cardsPerRow - 1) * cardSpacing;
            var availableCardWidth = availableArea.Width - totalSpacingWidth;
            var cardWidth = availableCardWidth / cardsPerRow;

            var currentY = availableArea.Y;

            // Layout cards in rows
            for (int row = 0; row < totalRows; row++)
            {
                var cardsInThisRow = Math.Min(cardsPerRow, controls.Length - (row * cardsPerRow));
                var rowStartIndex = row * cardsPerRow;

                // Calculate starting X position for centering the row
                var totalRowWidth = (cardsInThisRow * cardWidth) + ((cardsInThisRow - 1) * cardSpacing);
                var startX = availableArea.X + (availableArea.Width - totalRowWidth) / 2;

                var maxHeightInRow = 0;

                // Position cards in this row
                for (int cardIndex = 0; cardIndex < cardsInThisRow; cardIndex++)
                {
                    var controlIndex = rowStartIndex + cardIndex;
                    if (controlIndex >= controls.Length) break;

                    var control = controls[controlIndex];
                    var cardX = startX + (cardIndex * (cardWidth + cardSpacing));

                    // Center the control within its allocated card space
                    var centerX = cardX + (cardWidth - control.Width) / 2;
                    control.Location = new Point(Math.Max(cardX, centerX), currentY);

                    maxHeightInRow = Math.Max(maxHeightInRow, control.Height);
                }

                currentY += maxHeightInRow + rowSpacing;
            }
        }

        private static int CalculateCardsPerRow(int availableWidth)
        {
            // Calculate optimal number of cards per row based on available width
            if (availableWidth >= 1400) return 3;      // Large screens: 3 cards per row
            if (availableWidth >= 1000) return 2;      // Medium screens: 2 cards per row
            return 1;                                   // Small screens: 1 card per row
        }

        // Helper methods
        private static void ApplyMultiColumnLayout(Control[] controls, Rectangle availableArea, int columns)
        {
            var columnWidth = (availableArea.Width - (columns - 1) * Spacing.ElementSpacing) / columns;
            var controlsPerColumn = (int)Math.Ceiling((double)controls.Length / columns);

            for (int col = 0; col < columns; col++)
            {
                var columnX = availableArea.X + col * (columnWidth + Spacing.ElementSpacing);
                var currentY = availableArea.Y;

                var startIndex = col * controlsPerColumn;
                var endIndex = Math.Min(startIndex + controlsPerColumn, controls.Length);

                for (int i = startIndex; i < endIndex; i++)
                {
                    var control = controls[i];
                    var centerX = columnX + (columnWidth - control.Width) / 2;
                    control.Location = new Point(Math.Max(columnX, centerX), currentY);
                    currentY += control.Height + Spacing.ElementSpacing;
                }
            }
        }

        private static void ApplySingleColumnLayout(Control[] controls, Rectangle availableArea)
        {
            var currentY = availableArea.Y;

            foreach (var control in controls)
            {
                var centerX = availableArea.X + (availableArea.Width - control.Width) / 2;
                control.Location = new Point(centerX, currentY);
                currentY += control.Height + Spacing.ElementSpacing;
            }
        }

        private static Control[] GetLayoutableControls(Control container)
        {
            return container.Controls
                .Cast<Control>()
                .Where(c => c.Visible && c.Width > 0 && c.Height > 0)
                .OrderBy(c => c.TabIndex)
                .ToArray();
        }

        private static int CalculateOptimalColumns(int controlCount, int availableWidth)
        {
            if (availableWidth > 1400) return Math.Min(4, controlCount);
            if (availableWidth > 1000) return Math.Min(3, controlCount);
            if (availableWidth > 600) return Math.Min(2, controlCount);
            return 1;
        }

        // Auto-layout for DataGridView
        public static void ApplyDataGridLayout(DataGridView grid, Control container)
        {
            if (grid == null || container == null || container.Width <= 0 || container.Height <= 0) return;

            try
            {
                var margin = Spacing.ContainerMargin;
                var availableWidth = container.Width - (margin * 2);
                var availableHeight = container.Height - (margin * 2);

                // Ensure we have positive dimensions
                if (availableWidth <= 0 || availableHeight <= 0) return;

                // Center the grid with optimal size
                var optimalWidth = Math.Min(availableWidth, (int)(availableWidth * 0.95));
                var optimalHeight = Math.Min(availableHeight, (int)(availableHeight * 0.9));

                // Ensure minimum sizes
                optimalWidth = Math.Max(optimalWidth, 300);
                optimalHeight = Math.Max(optimalHeight, 200);

                var centerX = Math.Max(0, (container.Width - optimalWidth) / 2);
                var centerY = Math.Max(0, (container.Height - optimalHeight) / 2);

                grid.Location = new Point(centerX, centerY);
                grid.Size = new Size(optimalWidth, optimalHeight);
            }
            catch (Exception ex)
            {
                // Log error but don't crash
                System.Diagnostics.Debug.WriteLine($"ApplyDataGridLayout error: {ex.Message}");
            }
        }

        // Apply smart layout to entire form
        public static void ApplyFormLayout(Form form)
        {
            if (form == null) return;

            try
            {
                // Apply responsive design
                ResponsiveManager.ApplyResponsiveDesign(form);

                // Apply smart layout to all containers in the form
                ApplyFormLayoutRecursive(form);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ApplyFormLayout error: {ex.Message}");
            }
        }

        private static void ApplyFormLayoutRecursive(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                // Apply layout to panels and containers
                if (control is Panel panel && panel.Controls.Count > 0)
                {
                    ApplySmartLayout(panel, LayoutMode.Adaptive);
                }

                // Recursively apply to child controls
                if (control.HasChildren)
                {
                    ApplyFormLayoutRecursive(control);
                }
            }
        }


    }
}
