using System.Windows.Forms;
using FontAwesome.Sharp;
using Guna.UI2.WinForms;
using ModernWinApp.Forms;

namespace ModernWinApp;

public partial class Form1 : Form
{
    private IconButton? currentBtn = null;
    private Panel leftBorderBtn;
    private Form? currentChildForm = null;

    // Sidebar toggle variables
    private bool sidebarCollapsed = false;
    private int sidebarExpandedWidth;
    private int sidebarCollapsedWidth = 60;
    private System.Windows.Forms.Timer sidebarAnimationTimer;
    private int animationStep = 0;
    private const int animationSteps = 10;
    private IconButton toggleButton;
    private ToolTip buttonToolTip;

    public Form1()
    {
        InitializeComponent();

        // Initialize responsive design
        Theme.ResponsiveManager.UpdateScreenSize();

        // Initialize sidebar dimensions
        sidebarExpandedWidth = Theme.ResponsiveManager.Dimensions.SidebarWidth;

        // Initialize animation timer
        sidebarAnimationTimer = new System.Windows.Forms.Timer();
        sidebarAnimationTimer.Interval = 20; // 20ms for smooth animation
        sidebarAnimationTimer.Tick += SidebarAnimationTimer_Tick;

        leftBorderBtn = new Panel
        {
            Size = new Size(3, Theme.ResponsiveManager.Dimensions.MenuButtonHeight),
            BackColor = Theme.ThemeManager.Colors.Accent
        };
        panelMenu.Controls.Add(leftBorderBtn);
        leftBorderBtn.Visible = false;

        // Initialize tooltip
        buttonToolTip = new ToolTip();

        // Create toggle button
        CreateToggleButton();

        // Set tooltip for toggle button
        buttonToolTip.SetToolTip(toggleButton, "تبديل القائمة الجانبية (Ctrl+B)");

        FormBorderStyle = FormBorderStyle.None;
        Text = string.Empty;
        ControlBox = false;
        DoubleBuffered = true;

        // Remove any margins and padding
        Margin = new Padding(0);
        Padding = new Padding(0);

        // Apply RTL settings
        RightToLeft = RightToLeft.Yes;
        RightToLeftLayout = true;

        // Set window to maximized state
        WindowState = FormWindowState.Maximized;

        // Ensure the form covers the entire screen
        TopMost = false;
        ShowInTaskbar = true;

        // Set initial form
        OpenChildForm(new HRDashboardForm());
        lblTitle.Text = "لوحة المعلومات";
        ActivateButton(btnDashboard);

        // Subscribe to theme and responsive changes
        Theme.ThemeManager.ThemeChanged += (s, e) => UpdateActiveButtonColors();
        Theme.ResponsiveManager.ScreenSizeChanged += (s, e) => UpdateResponsiveLayout();
        Theme.ModernColorScheme.ThemeChanged += (s, e) => UpdateModernTheme();

        // Apply responsive design
        Theme.ResponsiveManager.ApplyResponsiveDesign(this);

        // Enable key preview for keyboard shortcuts
        this.KeyPreview = true;
        this.KeyDown += Form1_KeyDown;
    }

    private void Form1_KeyDown(object sender, KeyEventArgs e)
    {
        // Toggle sidebar with Ctrl+B or F9
        if ((e.Control && e.KeyCode == Keys.B) || e.KeyCode == Keys.F9)
        {
            ToggleSidebar();
            e.Handled = true;
        }
    }

    private void ActivateButton(object senderBtn)
    {
        if (senderBtn != null)
        {
            DisableButton();
            currentBtn = (IconButton)senderBtn;

            // Highlight active button
            currentBtn.BackColor = Theme.ThemeManager.Colors.AccentDark;
            currentBtn.ForeColor = Theme.ThemeManager.Colors.White;
            currentBtn.IconColor = Theme.ThemeManager.Colors.White;
            currentBtn.Font = Theme.ThemeManager.Fonts.Subtitle;

            // Show and position left border
            leftBorderBtn.Location = new Point(panelMenu.Width - 3, currentBtn.Location.Y);
            leftBorderBtn.BackColor = Theme.ThemeManager.Colors.White;
            leftBorderBtn.BringToFront();
            leftBorderBtn.Visible = true;
        }
    }

    private void DisableButton()
    {
        if (currentBtn != null)
        {
            currentBtn.BackColor = Theme.ThemeManager.Colors.Sidebar;
            currentBtn.ForeColor = Theme.ThemeManager.Colors.White;
            currentBtn.IconColor = Theme.ThemeManager.Colors.White;
            currentBtn.Font = Theme.ThemeManager.Fonts.Regular;
        }
    }

    private void UpdateActiveButtonColors()
    {
        if (currentBtn != null)
        {
            currentBtn.BackColor = Theme.ThemeManager.Colors.AccentDark;
            currentBtn.ForeColor = Theme.ThemeManager.Colors.White;
            currentBtn.IconColor = Theme.ThemeManager.Colors.White;
            leftBorderBtn.BackColor = Theme.ThemeManager.Colors.White;
        }

        // Update other menu buttons
        foreach (IconButton btn in panelMenu.Controls.OfType<IconButton>())
        {
            if (btn != currentBtn)
            {
                btn.BackColor = Theme.ThemeManager.Colors.Sidebar;
                btn.ForeColor = Theme.ThemeManager.Colors.White;
                btn.IconColor = Theme.ThemeManager.Colors.White;
            }
        }
    }

    private void OpenChildForm(Form childForm)
    {
        currentChildForm?.Close();

        currentChildForm = childForm;
        childForm.TopLevel = false;
        childForm.FormBorderStyle = FormBorderStyle.None;
        childForm.Dock = DockStyle.Fill;
        Theme.ThemeManager.ApplyTheme(childForm);
        panelDesktop.Controls.Add(childForm);
        panelDesktop.Tag = childForm;
        childForm.BringToFront();
        childForm.Show();
    }

    protected void btnDashboard_Click(object sender, EventArgs e)
    {
        ActivateButton(sender);
        OpenChildForm(new HRDashboardForm());
        lblTitle.Text = "لوحة المعلومات";
    }

    protected void btnEmployees_Click(object sender, EventArgs e)
    {
        ActivateButton(sender);
        OpenChildForm(new EmployeeManagementForm());
        lblTitle.Text = "إدارة الموظفين";
    }

    protected void btnAttendance_Click(object sender, EventArgs e)
    {
        ActivateButton(sender);
        OpenChildForm(new AttendanceTrackingForm());
        lblTitle.Text = "تتبع الحضور والانصراف";
    }

    protected void btnSalaries_Click(object sender, EventArgs e)
    {
        ActivateButton(sender);
        OpenChildForm(new PayrollManagementForm());
        lblTitle.Text = "إدارة كشوف الرواتب";
    }

    protected void btnSettings_Click(object sender, EventArgs e)
    {
        ActivateButton(sender);
        OpenChildForm(new SystemSettingsForm());
        lblTitle.Text = "إعدادات النظام";
    }

    protected void panelTitleBar_MouseDown(object sender, MouseEventArgs e)
    {
        if (e.Button == MouseButtons.Left)
        {
            ReleaseCapture();
            SendMessage(Handle, 0x112, 0xf012, 0);
        }
    }

    private void UpdateResponsiveLayout()
    {
        // Update sidebar dimensions
        sidebarExpandedWidth = Theme.ResponsiveManager.Dimensions.SidebarWidth;
        sidebarCollapsedWidth = (int)(60 * Theme.ResponsiveManager.ScaleFactor);

        // Update current sidebar width if not collapsed
        if (!sidebarCollapsed)
        {
            panelMenu.Width = sidebarExpandedWidth;
            toggleButton.Width = sidebarExpandedWidth;
        }
        else
        {
            panelMenu.Width = sidebarCollapsedWidth;
            toggleButton.Width = sidebarCollapsedWidth;
        }

        // Update left border button size
        leftBorderBtn.Size = new Size(3, Theme.ResponsiveManager.Dimensions.MenuButtonHeight);

        // Update toggle button size
        toggleButton.Height = Theme.ResponsiveManager.Dimensions.MenuButtonHeight;
        toggleButton.IconSize = (int)(Theme.ResponsiveManager.ScaleFactor * 20);

        // Reapply responsive design to all controls
        Theme.ResponsiveManager.ApplyResponsiveDesign(this);

        // Update menu buttons layout
        UpdateMenuButtonsLayout();

        // Update desktop panel
        panelDesktop.Left = panelMenu.Right;
        panelDesktop.Width = this.Width - panelMenu.Width;

        // Update current child form if exists
        if (currentChildForm != null)
        {
            Theme.ResponsiveManager.ApplyResponsiveDesign(currentChildForm);
        }

        // Force layout update
        this.PerformLayout();
        this.Refresh();
    }

    private void UpdateModernTheme()
    {
        // Update background colors
        this.BackColor = Theme.ThemeManager.Colors.Background;

        // Update toggle button colors
        if (toggleButton != null)
        {
            toggleButton.BackColor = Theme.ThemeManager.Colors.Sidebar;
            toggleButton.ForeColor = Theme.ThemeManager.Colors.White;
            toggleButton.IconColor = Theme.ThemeManager.Colors.White;
        }

        // Update all panels and controls with new theme colors
        UpdateControlTheme(this);

        // Update active button colors
        UpdateActiveButtonColors();

        // Update current child form if exists
        if (currentChildForm != null)
        {
            Theme.ThemeManager.ApplyTheme(currentChildForm);
        }

        // Force refresh
        this.Refresh();
    }

    private void UpdateControlTheme(Control parent)
    {
        foreach (Control control in parent.Controls)
        {
            if (control is Panel panel)
            {
                if (panel.Name == "panelMenu")
                    panel.BackColor = Theme.ThemeManager.Colors.Sidebar;
                else if (panel.Name == "panelDesktop")
                    panel.BackColor = Theme.ThemeManager.Colors.Background;
                else if (panel.Name == "panelTitleBar")
                    panel.BackColor = Theme.ThemeManager.Colors.Secondary;
                else
                    panel.BackColor = Theme.ThemeManager.Colors.Card;
            }
            else if (control is Label label)
            {
                label.ForeColor = Theme.ThemeManager.Colors.Text;
            }

            // Recursively update child controls
            if (control.HasChildren)
            {
                UpdateControlTheme(control);
            }
        }
    }

    private void CreateToggleButton()
    {
        toggleButton = new IconButton
        {
            Size = new Size(sidebarExpandedWidth, Theme.ResponsiveManager.Dimensions.MenuButtonHeight),
            Location = new Point(0, 0),
            FlatStyle = FlatStyle.Flat,
            BackColor = Theme.ThemeManager.Colors.Sidebar,
            ForeColor = Theme.ThemeManager.Colors.White,
            IconChar = IconChar.Bars,
            IconColor = Theme.ThemeManager.Colors.White,
            IconSize = (int)(Theme.ResponsiveManager.ScaleFactor * 20),
            Font = Theme.ResponsiveManager.Fonts.MenuButton,
            Text = "القائمة",
            TextAlign = ContentAlignment.MiddleRight,
            ImageAlign = ContentAlignment.MiddleLeft,
            Padding = new Padding(10, 0, 20, 0),
            RightToLeft = RightToLeft.Yes,
            Cursor = Cursors.Hand
        };

        toggleButton.FlatAppearance.BorderSize = 0;
        toggleButton.Click += ToggleButton_Click;
        panelMenu.Controls.Add(toggleButton);
        toggleButton.BringToFront();
    }

    private void ToggleButton_Click(object sender, EventArgs e)
    {
        ToggleSidebar();
    }

    private void ToggleSidebar()
    {
        sidebarCollapsed = !sidebarCollapsed;
        animationStep = 0;
        sidebarAnimationTimer.Start();

        // Update toggle button icon
        toggleButton.IconChar = sidebarCollapsed ? IconChar.ChevronRight : IconChar.Bars;

        // Hide/show button text during animation
        if (sidebarCollapsed)
        {
            toggleButton.Text = "";
            toggleButton.TextAlign = ContentAlignment.MiddleCenter;
            toggleButton.ImageAlign = ContentAlignment.MiddleCenter;
        }
        else
        {
            toggleButton.Text = "القائمة";
            toggleButton.TextAlign = ContentAlignment.MiddleRight;
            toggleButton.ImageAlign = ContentAlignment.MiddleLeft;
        }
    }

    private void SidebarAnimationTimer_Tick(object sender, EventArgs e)
    {
        animationStep++;

        int targetWidth = sidebarCollapsed ? sidebarCollapsedWidth : sidebarExpandedWidth;
        int currentWidth = panelMenu.Width;
        int widthDifference = targetWidth - currentWidth;

        // Calculate smooth animation step
        int stepSize = widthDifference / (animationSteps - animationStep + 1);

        if (Math.Abs(stepSize) < 1)
            stepSize = widthDifference > 0 ? 1 : -1;

        int newWidth = currentWidth + stepSize;

        // Ensure we don't overshoot
        if ((sidebarCollapsed && newWidth <= sidebarCollapsedWidth) ||
            (!sidebarCollapsed && newWidth >= sidebarExpandedWidth))
        {
            newWidth = targetWidth;
            sidebarAnimationTimer.Stop();
        }

        // Apply new width
        panelMenu.Width = newWidth;
        toggleButton.Width = newWidth;

        // Update button layout for collapsed state
        UpdateMenuButtonsLayout();

        // Update desktop panel location
        panelDesktop.Left = panelMenu.Right;
        panelDesktop.Width = this.Width - panelMenu.Width;
    }

    private void UpdateMenuButtonsLayout()
    {
        foreach (Control control in panelMenu.Controls)
        {
            if (control is IconButton btn && btn != toggleButton)
            {
                btn.Width = panelMenu.Width;

                if (sidebarCollapsed)
                {
                    btn.Text = "";
                    btn.TextAlign = ContentAlignment.MiddleCenter;
                    btn.ImageAlign = ContentAlignment.MiddleCenter;
                    btn.Padding = new Padding(0);

                    // Add tooltips for collapsed buttons
                    if (btn == btnDashboard) buttonToolTip.SetToolTip(btn, "لوحة المعلومات");
                    else if (btn == btnEmployees) buttonToolTip.SetToolTip(btn, "إدارة الموظفين");
                    else if (btn == btnAttendance) buttonToolTip.SetToolTip(btn, "الحضور والانصراف");
                    else if (btn == btnSalaries) buttonToolTip.SetToolTip(btn, "كشوف الرواتب");
                    else if (btn == btnSettings) buttonToolTip.SetToolTip(btn, "إعدادات النظام");
                }
                else
                {
                    // Restore original text based on button
                    if (btn == btnDashboard) btn.Text = "لوحة المعلومات";
                    else if (btn == btnEmployees) btn.Text = "إدارة الموظفين";
                    else if (btn == btnAttendance) btn.Text = "الحضور والانصراف";
                    else if (btn == btnSalaries) btn.Text = "كشوف الرواتب";
                    else if (btn == btnSettings) btn.Text = "إعدادات النظام";

                    btn.TextAlign = ContentAlignment.MiddleRight;
                    btn.ImageAlign = ContentAlignment.MiddleLeft;
                    btn.Padding = new Padding(10, 0, 20, 0);

                    // Remove tooltips when expanded
                    buttonToolTip.SetToolTip(btn, "");
                }
            }
        }

        // Update left border position
        if (currentBtn != null && leftBorderBtn.Visible)
        {
            leftBorderBtn.Location = new Point(panelMenu.Width - 3, currentBtn.Location.Y);
        }
    }

    [System.Runtime.InteropServices.DllImport("user32.dll")]
    public static extern int SendMessage(IntPtr hWnd, int Msg, int wParam, int lParam);
    [System.Runtime.InteropServices.DllImport("user32.dll")]
    public static extern bool ReleaseCapture();
}
