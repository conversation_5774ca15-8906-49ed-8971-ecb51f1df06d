using System.Windows.Forms;
using FontAwesome.Sharp;
using Guna.UI2.WinForms;
using ModernWinApp.Forms;

namespace ModernWinApp;

public partial class Form1 : Form
{
    private IconButton? currentBtn = null;
    private Panel leftBorderBtn;
    private Form? currentChildForm = null;

    public Form1()
    {
        InitializeComponent();
        leftBorderBtn = new Panel
        {
            Size = new Size(3, 56),
            BackColor = Theme.ThemeManager.Colors.Accent
        };
        panelMenu.Controls.Add(leftBorderBtn);
        leftBorderBtn.Visible = false;

        FormBorderStyle = FormBorderStyle.None;
        Text = string.Empty;
        ControlBox = false;
        DoubleBuffered = true;

        // Remove any margins and padding
        Margin = new Padding(0);
        Padding = new Padding(0);

        // Set window to maximized state
        WindowState = FormWindowState.Maximized;

        // Ensure the form covers the entire screen
        TopMost = false;
        ShowInTaskbar = true;

        // Set initial form
        OpenChildForm(new HRDashboardForm());
        lblTitle.Text = "لوحة المعلومات";
        ActivateButton(btnDashboard);

        // Subscribe to theme changes
        Theme.ThemeManager.ThemeChanged += (s, e) => UpdateActiveButtonColors();
    }

    private void ActivateButton(object senderBtn)
    {
        if (senderBtn != null)
        {
            DisableButton();
            currentBtn = (IconButton)senderBtn;

            // Highlight active button
            currentBtn.BackColor = Theme.ThemeManager.Colors.DarkAccent;
            currentBtn.ForeColor = Theme.ThemeManager.Colors.White;
            currentBtn.IconColor = Theme.ThemeManager.Colors.White;
            currentBtn.Font = Theme.ThemeManager.Fonts.Subtitle;

            // Show and position left border
            leftBorderBtn.Location = new Point(panelMenu.Width - 3, currentBtn.Location.Y);
            leftBorderBtn.BackColor = Theme.ThemeManager.Colors.White;
            leftBorderBtn.BringToFront();
            leftBorderBtn.Visible = true;
        }
    }

    private void DisableButton()
    {
        if (currentBtn != null)
        {
            currentBtn.BackColor = Theme.ThemeManager.Colors.Sidebar;
            currentBtn.ForeColor = Theme.ThemeManager.Colors.White;
            currentBtn.IconColor = Theme.ThemeManager.Colors.White;
            currentBtn.Font = Theme.ThemeManager.Fonts.Regular;
        }
    }

    private void UpdateActiveButtonColors()
    {
        if (currentBtn != null)
        {
            currentBtn.BackColor = Theme.ThemeManager.Colors.DarkAccent;
            currentBtn.ForeColor = Theme.ThemeManager.Colors.White;
            currentBtn.IconColor = Theme.ThemeManager.Colors.White;
            leftBorderBtn.BackColor = Theme.ThemeManager.Colors.White;
        }

        // Update other menu buttons
        foreach (IconButton btn in panelMenu.Controls.OfType<IconButton>())
        {
            if (btn != currentBtn)
            {
                btn.BackColor = Theme.ThemeManager.Colors.Sidebar;
                btn.ForeColor = Theme.ThemeManager.Colors.White;
                btn.IconColor = Theme.ThemeManager.Colors.White;
            }
        }
    }

    private void OpenChildForm(Form childForm)
    {
        currentChildForm?.Close();

        currentChildForm = childForm;
        childForm.TopLevel = false;
        childForm.FormBorderStyle = FormBorderStyle.None;
        childForm.Dock = DockStyle.Fill;
        Theme.ThemeManager.ApplyTheme(childForm);
        panelDesktop.Controls.Add(childForm);
        panelDesktop.Tag = childForm;
        childForm.BringToFront();
        childForm.Show();
    }

    protected void btnDashboard_Click(object sender, EventArgs e)
    {
        ActivateButton(sender);
        OpenChildForm(new HRDashboardForm());
        lblTitle.Text = "لوحة المعلومات";
    }

    protected void btnEmployees_Click(object sender, EventArgs e)
    {
        ActivateButton(sender);
        OpenChildForm(new EmployeeManagementForm());
        lblTitle.Text = "إدارة الموظفين";
    }

    protected void btnAttendance_Click(object sender, EventArgs e)
    {
        ActivateButton(sender);
        OpenChildForm(new AttendanceTrackingForm());
        lblTitle.Text = "تتبع الحضور والانصراف";
    }

    protected void btnSalaries_Click(object sender, EventArgs e)
    {
        ActivateButton(sender);
        OpenChildForm(new PayrollManagementForm());
        lblTitle.Text = "إدارة كشوف الرواتب";
    }

    protected void btnSettings_Click(object sender, EventArgs e)
    {
        ActivateButton(sender);
        OpenChildForm(new SystemSettingsForm());
        lblTitle.Text = "إعدادات النظام";
    }

    protected void panelTitleBar_MouseDown(object sender, MouseEventArgs e)
    {
        if (e.Button == MouseButtons.Left)
        {
            ReleaseCapture();
            SendMessage(Handle, 0x112, 0xf012, 0);
        }
    }

    [System.Runtime.InteropServices.DllImport("user32.dll")]
    public static extern int SendMessage(IntPtr hWnd, int Msg, int wParam, int lParam);
    [System.Runtime.InteropServices.DllImport("user32.dll")]
    public static extern bool ReleaseCapture();
}
