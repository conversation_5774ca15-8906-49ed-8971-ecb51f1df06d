using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class AttendanceTrackingForm : Form
    {
        private Guna2DataGridView grid;
        private Guna2DateTimePicker calendar;
        private Panel actionsGroup;

        public AttendanceTrackingForm()
        {
            InitializeComponent();
            InitializeAttendance();
        }

        private void InitializeAttendance()
        {
            // Create container panel for proper layout
            var containerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Initialize top panel for controls
            var topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 100,
                BackColor = Color.FromArgb(40, 39, 84),
                Padding = new Padding(10)
            };

            // Initialize calendar
            calendar = new Guna2DateTimePicker
            {
                Format = DateTimePickerFormat.Long,
                Font = new Font("Segoe UI", 10F),
                FillColor = Color.FromArgb(50, 49, 94),
                ForeColor = Color.White,
                Size = new Size(280, 40),
                Location = new Point(20, 30),
                BorderRadius = 8
            };

            // إضافة تسمية للتاريخ
            var dateLabel = new Label
            {
                Text = "تاريخ الحضور:",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(20, 5),
                AutoSize = true
            };

            // Initialize quick actions panel
            actionsGroup = new Panel
            {
                Dock = DockStyle.Right,
                Width = 500,
                Padding = new Padding(10)
            };

            var checkInBtn = new Guna2Button
            {
                Text = "تسجيل حضور",
                FillColor = Color.FromArgb(40, 167, 69),
                HoverState = { FillColor = Color.FromArgb(34, 139, 58) },
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(110, 40),
                Location = new Point(380, 25),
                BorderRadius = 8,
                Cursor = Cursors.Hand
            };

            var checkOutBtn = new Guna2Button
            {
                Text = "تسجيل انصراف",
                FillColor = Color.FromArgb(220, 53, 69),
                HoverState = { FillColor = Color.FromArgb(200, 35, 51) },
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(110, 40),
                Location = new Point(260, 25),
                BorderRadius = 8,
                Cursor = Cursors.Hand
            };

            var reportBtn = new Guna2Button
            {
                Text = "تقرير الحضور",
                FillColor = Color.FromArgb(0, 123, 255),
                HoverState = { FillColor = Color.FromArgb(0, 105, 217) },
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(110, 40),
                Location = new Point(140, 25),
                BorderRadius = 8,
                Cursor = Cursors.Hand
            };

            var exportBtn = new Guna2Button
            {
                Text = "تصدير",
                FillColor = Color.FromArgb(108, 117, 125),
                HoverState = { FillColor = Color.FromArgb(90, 98, 104) },
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Size = new Size(100, 40),
                Location = new Point(30, 25),
                BorderRadius = 8,
                Cursor = Cursors.Hand
            };

            actionsGroup.Controls.AddRange(new Control[] { checkInBtn, checkOutBtn, reportBtn, exportBtn });

            topPanel.Controls.Add(dateLabel);
            topPanel.Controls.Add(calendar);
            topPanel.Controls.Add(actionsGroup);

            // Initialize grid
            grid = new Guna2DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.FromArgb(34, 33, 74),
                ForeColor = Color.White,
                ThemeStyle = {
                    HeaderStyle = {
                        BackColor = Color.FromArgb(37, 36, 81),
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                    },
                    RowsStyle = {
                        BackColor = Color.FromArgb(34, 33, 74),
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 9F)
                    },
                    AlternatingRowsStyle = {
                        BackColor = Color.FromArgb(31, 30, 68),
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 9F)
                    }
                },
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                GridColor = Color.FromArgb(50, 49, 90),
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                DefaultCellStyle = { SelectionBackColor = Color.FromArgb(37, 36, 81) },
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RightToLeft = RightToLeft.Yes
            };

            grid.Columns.Add("EmployeeId", "الرقم الوظيفي");
            grid.Columns.Add("Name", "اسم الموظف");
            grid.Columns.Add("Department", "القسم");
            grid.Columns.Add("CheckIn", "وقت الحضور");
            grid.Columns.Add("CheckOut", "وقت الانصراف");
            grid.Columns.Add("WorkHours", "ساعات العمل");
            grid.Columns.Add("Status", "الحالة");
            grid.Columns.Add("Notes", "ملاحظات");
            grid.Columns.Add("Actions", "الإجراءات");

            // تخصيص عرض الأعمدة
            grid.Columns["EmployeeId"].Width = 100;
            grid.Columns["Name"].Width = 150;
            grid.Columns["Department"].Width = 120;
            grid.Columns["CheckIn"].Width = 100;
            grid.Columns["CheckOut"].Width = 100;
            grid.Columns["WorkHours"].Width = 100;
            grid.Columns["Status"].Width = 100;
            grid.Columns["Notes"].Width = 200;
            grid.Columns["Actions"].Width = 100;

            // Add sample data
            grid.Rows.Add("1001", "أحمد محمد علي", "تقنية المعلومات", "08:00 ص", "04:00 م", "8 ساعات", "حاضر", "في الوقت المحدد", "تعديل");
            grid.Rows.Add("1002", "سارة أحمد خالد", "الموارد البشرية", "08:30 ص", "04:30 م", "8 ساعات", "متأخر", "تأخير 30 دقيقة", "تعديل");
            grid.Rows.Add("1003", "محمد علي حسن", "المالية", "--:--", "--:--", "0 ساعات", "غائب", "إجازة مرضية", "تعديل");
            grid.Rows.Add("1004", "فاطمة خالد عمر", "العمليات", "08:15 ص", "03:30 م", "7.25 ساعة", "خروج مبكر", "خروج مبكر بإذن", "تعديل");
            grid.Rows.Add("1005", "عمر يوسف سالم", "التسويق", "08:00 ص", "--:--", "-- ساعة", "حاضر", "لم ينصرف بعد", "تعديل");
            grid.Rows.Add("1006", "نورا حسام الدين", "تقنية المعلومات", "07:45 ص", "04:15 م", "8.5 ساعة", "حاضر", "حضور مبكر", "تعديل");
            grid.Rows.Add("1007", "خالد عبدالله", "المبيعات", "09:00 ص", "05:00 م", "8 ساعات", "متأخر", "تأخير ساعة", "تعديل");
            grid.Rows.Add("1008", "ريم محمد", "الموارد البشرية", "08:10 ص", "04:10 م", "8 ساعات", "حاضر", "تأخير بسيط", "تعديل");

            foreach (DataGridViewColumn column in grid.Columns)
            {
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
            }

            // Add controls to container in correct order
            containerPanel.Controls.Add(grid);      // Add grid first (will be at bottom/fill)
            containerPanel.Controls.Add(topPanel);  // Add top panel last (will be at top)

            // Add container to form
            this.Controls.Add(containerPanel);
        }
    }
}
