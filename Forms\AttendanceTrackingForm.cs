using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class AttendanceTrackingForm : Form
    {
        private Guna2DataGridView grid;
        private Guna2DateTimePicker calendar;
        private Panel actionsGroup;

        public AttendanceTrackingForm()
        {
            InitializeComponent();
            InitializeAttendance();
        }

        private void InitializeAttendance()
        {
            // Create container panel for proper layout
            var containerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Initialize top panel for controls
            var topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60
            };

            // Initialize calendar
            calendar = new Guna2DateTimePicker
            {
                Format = DateTimePickerFormat.Long,
                Font = new Font("Segoe UI", 10F),
                FillColor = Color.FromArgb(37, 36, 81),
                ForeColor = Color.White,
                Size = new Size(250, 40),
                Location = new Point(20, 10)
            };
            topPanel.Controls.Add(calendar);

            // Initialize quick actions panel
            actionsGroup = new Panel
            {
                Dock = DockStyle.Right,
                Width = 200
            };

            var checkInBtn = new Guna2Button
            {
                Text = "تسجيل حضور",
                FillColor = Color.FromArgb(0, 126, 249),
                Font = new Font("Segoe UI", 9F),
                Size = new Size(160, 35),
                Location = new Point(20, 5),
                BorderRadius = 5,
                Cursor = Cursors.Hand
            };

            var checkOutBtn = new Guna2Button
            {
                Text = "تسجيل انصراف",
                FillColor = Color.FromArgb(0, 126, 249),
                Font = new Font("Segoe UI", 9F),
                Size = new Size(160, 35),
                Location = new Point(20, 45),
                BorderRadius = 5,
                Cursor = Cursors.Hand
            };

            actionsGroup.Controls.AddRange(new Control[] { checkInBtn, checkOutBtn });
            topPanel.Controls.Add(actionsGroup);

            // Initialize grid
            grid = new Guna2DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.FromArgb(34, 33, 74),
                ForeColor = Color.White,
                ThemeStyle = {
                    HeaderStyle = { 
                        BackColor = Color.FromArgb(37, 36, 81), 
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                    },
                    RowsStyle = { 
                        BackColor = Color.FromArgb(34, 33, 74), 
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 9F)
                    },
                    AlternatingRowsStyle = {
                        BackColor = Color.FromArgb(31, 30, 68),
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 9F)
                    }
                },
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                GridColor = Color.FromArgb(50, 49, 90),
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                DefaultCellStyle = { SelectionBackColor = Color.FromArgb(37, 36, 81) },
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RightToLeft = RightToLeft.Yes
            };

            grid.Columns.Add("EmployeeId", "الرقم الوظيفي");
            grid.Columns.Add("Name", "اسم الموظف");
            grid.Columns.Add("CheckIn", "وقت الحضور");
            grid.Columns.Add("CheckOut", "وقت الانصراف");
            grid.Columns.Add("Status", "الحالة");
            grid.Columns.Add("Notes", "ملاحظات");

            // Add sample data
            grid.Rows.Add("1001", "أحمد محمد", "08:00 ص", "04:00 م", "حاضر", "");
            grid.Rows.Add("1002", "سارة أحمد", "08:30 ص", "04:30 م", "حاضر", "تأخير 30 دقيقة");
            grid.Rows.Add("1003", "محمد علي", "--:--", "--:--", "غائب", "إجازة مرضية");
            grid.Rows.Add("1004", "فاطمة خالد", "08:15 ص", "03:30 م", "حاضر", "خروج مبكر");
            grid.Rows.Add("1005", "عمر يوسف", "08:00 ص", "--:--", "حاضر", "");

            foreach (DataGridViewColumn column in grid.Columns)
            {
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
            }

            // Add controls to container in correct order
            containerPanel.Controls.Add(grid);      // Add grid first (will be at bottom/fill)
            containerPanel.Controls.Add(topPanel);  // Add top panel last (will be at top)

            // Add container to form
            this.Controls.Add(containerPanel);
        }
    }
}
