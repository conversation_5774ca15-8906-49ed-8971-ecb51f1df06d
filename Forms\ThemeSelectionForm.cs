using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace ModernWinApp.Forms
{
    public partial class ThemeSelectionForm : Form
    {
        private Panel themePreviewPanel;
        private Label selectedThemeLabel;
        private Guna2Button applyButton;
        private Guna2Button cancelButton;
        private Theme.ModernColorScheme.ColorTheme selectedTheme;

        public ThemeSelectionForm()
        {
            InitializeComponent();
            InitializeThemeSelection();
            selectedTheme = Theme.ModernColorScheme.CurrentTheme;
        }

        private void InitializeThemeSelection()
        {
            this.Text = "اختيار الثيم العصري";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Theme.ThemeManager.Colors.Background;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateThemeSelectionUI();
        }

        private void CreateThemeSelectionUI()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(Theme.ResponsiveManager.Dimensions.Padding),
                BackColor = Theme.ThemeManager.Colors.Background
            };

            // Title
            var titleLabel = new Label
            {
                Text = "اختر الثيم المفضل لديك",
                Font = Theme.ResponsiveManager.Fonts.LargeTitle,
                ForeColor = Theme.ThemeManager.Colors.Text,
                Location = new Point(20, 20),
                Size = new Size(860, 50),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            // Theme Cards Container
            var themesContainer = new FlowLayoutPanel
            {
                Location = new Point(20, 80),
                Size = new Size(860, 480),
                FlowDirection = FlowDirection.RightToLeft,
                WrapContents = true,
                AutoScroll = true,
                BackColor = Theme.ThemeManager.Colors.Background
            };

            // Create theme cards
            CreateThemeCard(themesContainer, Theme.ModernColorScheme.ColorTheme.Ocean, "المحيط الأزرق",
                           Theme.ModernColorScheme.Ocean.Primary, Theme.ModernColorScheme.Ocean.Accent);

            CreateThemeCard(themesContainer, Theme.ModernColorScheme.ColorTheme.Forest, "الغابة الخضراء",
                           Theme.ModernColorScheme.Forest.Primary, Theme.ModernColorScheme.Forest.Accent);

            CreateThemeCard(themesContainer, Theme.ModernColorScheme.ColorTheme.Sunset, "غروب الشمس",
                           Theme.ModernColorScheme.Sunset.Primary, Theme.ModernColorScheme.Sunset.Accent);

            CreateThemeCard(themesContainer, Theme.ModernColorScheme.ColorTheme.Midnight, "منتصف الليل",
                           Theme.ModernColorScheme.Midnight.Primary, Theme.ModernColorScheme.Midnight.Accent);

            CreateThemeCard(themesContainer, Theme.ModernColorScheme.ColorTheme.Aurora, "الشفق القطبي",
                           Theme.ModernColorScheme.Aurora.Primary, Theme.ModernColorScheme.Aurora.Accent);

            CreateThemeCard(themesContainer, Theme.ModernColorScheme.ColorTheme.Corporate, "المؤسسي الاحترافي",
                           Theme.ModernColorScheme.Corporate.Primary, Theme.ModernColorScheme.Corporate.Accent);

            // Selected Theme Info
            var infoPanel = new Panel
            {
                Location = new Point(20, 580),
                Size = new Size(860, 60),
                BackColor = Theme.ThemeManager.Colors.Card,
                BorderStyle = BorderStyle.FixedSingle
            };

            selectedThemeLabel = new Label
            {
                Text = $"الثيم المختار: {Theme.ModernColorScheme.GetThemeDisplayName(selectedTheme)}",
                Font = Theme.ResponsiveManager.Fonts.Subtitle,
                ForeColor = Theme.ThemeManager.Colors.Text,
                Location = new Point(20, 20),
                Size = new Size(400, 25),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // Buttons
            applyButton = new Guna2Button
            {
                Text = "تطبيق الثيم",
                Size = new Size(150, 45),
                Location = new Point(600, 10),
                FillColor = Theme.ThemeManager.Colors.Success,
                HoverState = { FillColor = Color.FromArgb(34, 197, 94) },
                BorderRadius = Theme.ResponsiveManager.Dimensions.BorderRadius,
                Font = Theme.ResponsiveManager.Fonts.Regular,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };

            cancelButton = new Guna2Button
            {
                Text = "إلغاء",
                Size = new Size(100, 45),
                Location = new Point(760, 10),
                FillColor = Theme.ThemeManager.Colors.Surface,
                HoverState = { FillColor = Theme.ThemeManager.Colors.Border },
                BorderRadius = Theme.ResponsiveManager.Dimensions.BorderRadius,
                Font = Theme.ResponsiveManager.Fonts.Regular,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };

            applyButton.Click += ApplyButton_Click;
            cancelButton.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            infoPanel.Controls.AddRange(new Control[] { selectedThemeLabel, applyButton, cancelButton });

            mainPanel.Controls.AddRange(new Control[] { titleLabel, themesContainer, infoPanel });
            this.Controls.Add(mainPanel);
        }

        private void CreateThemeCard(FlowLayoutPanel container, Theme.ModernColorScheme.ColorTheme theme,
                                   string displayName, Color primaryColor, Color accentColor)
        {
            var card = new Panel
            {
                Size = new Size(270, 220),
                Margin = new Padding(10),
                BackColor = primaryColor,
                BorderStyle = BorderStyle.FixedSingle,
                Cursor = Cursors.Hand
            };

            // Theme name
            var nameLabel = new Label
            {
                Text = displayName,
                Font = Theme.ResponsiveManager.Fonts.Subtitle,
                ForeColor = Color.White,
                Location = new Point(10, 10),
                Size = new Size(250, 30),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            // Color preview
            var colorPreview = new Panel
            {
                Location = new Point(20, 50),
                Size = new Size(230, 120),
                BackColor = accentColor
            };

            // Gradient effect
            var gradientPanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(230, 40),
                BackColor = Color.FromArgb(100, Color.White)
            };

            colorPreview.Controls.Add(gradientPanel);

            // Selection indicator
            var selectionBorder = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(270, 220),
                BackColor = Color.Transparent,
                BorderStyle = BorderStyle.None,
                Visible = theme == selectedTheme
            };

            if (theme == selectedTheme)
            {
                card.BorderStyle = BorderStyle.Fixed3D;
                var checkMark = new Label
                {
                    Text = "✓",
                    Font = new Font("Arial", 20, FontStyle.Bold),
                    ForeColor = Color.White,
                    BackColor = Theme.ThemeManager.Colors.Success,
                    Location = new Point(220, 180),
                    Size = new Size(40, 30),
                    TextAlign = ContentAlignment.MiddleCenter
                };
                card.Controls.Add(checkMark);
            }

            // Sample elements
            var sampleButton = new Panel
            {
                Location = new Point(20, 180),
                Size = new Size(80, 25),
                BackColor = accentColor
            };

            var sampleText = new Label
            {
                Text = "نموذج",
                Font = Theme.ResponsiveManager.Fonts.Small,
                ForeColor = Color.White,
                Location = new Point(110, 180),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };

            card.Controls.AddRange(new Control[] { nameLabel, colorPreview, sampleButton, sampleText });

            // Click event
            card.Click += (s, e) => SelectTheme(theme);
            nameLabel.Click += (s, e) => SelectTheme(theme);
            colorPreview.Click += (s, e) => SelectTheme(theme);

            container.Controls.Add(card);
        }

        private void SelectTheme(Theme.ModernColorScheme.ColorTheme theme)
        {
            selectedTheme = theme;
            selectedThemeLabel.Text = $"الثيم المختار: {Theme.ModernColorScheme.GetThemeDisplayName(theme)}";

            // Update visual selection
            foreach (Control card in ((FlowLayoutPanel)this.Controls[0].Controls[1]).Controls)
            {
                if (card is Panel panel)
                {
                    // Remove existing checkmarks
                    var existingCheck = panel.Controls.OfType<Label>().FirstOrDefault(l => l.Text == "✓");
                    if (existingCheck != null)
                        panel.Controls.Remove(existingCheck);

                    panel.BorderStyle = BorderStyle.FixedSingle;
                }
            }

            // Add checkmark to selected theme
            var selectedCard = ((FlowLayoutPanel)this.Controls[0].Controls[1]).Controls
                .OfType<Panel>().ElementAt((int)theme);

            selectedCard.BorderStyle = BorderStyle.Fixed3D;
            var checkMark = new Label
            {
                Text = "✓",
                Font = new Font("Arial", 20, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Theme.ThemeManager.Colors.Success,
                Location = new Point(220, 180),
                Size = new Size(40, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            selectedCard.Controls.Add(checkMark);
        }

        private void ApplyButton_Click(object sender, EventArgs e)
        {
            try
            {
                Theme.ModernColorScheme.ChangeTheme(selectedTheme);

                MessageBox.Show($"تم تطبيق ثيم '{Theme.ModernColorScheme.GetThemeDisplayName(selectedTheme)}' بنجاح!\n" +
                              "سيتم تطبيق التغييرات على النوافذ الجديدة.",
                              "نجح التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تطبيق الثيم:\n{ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.ResumeLayout(false);
        }
    }
}
