{"version": 3, "targets": {"net9.0-windows7.0": {"FontAwesome.Sharp/6.6.0": {"type": "package", "compile": {"lib/net8.0-windows7.0/FontAwesome.Sharp.dll": {}}, "runtime": {"lib/net8.0-windows7.0/FontAwesome.Sharp.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App"]}, "Guna.UI2.WinForms/2.0.4.7": {"type": "package", "dependencies": {"System.Management": "7.0.0"}, "compile": {"lib/net7.0-windows7.0/Guna.UI2.dll": {}}, "runtime": {"lib/net7.0-windows7.0/Guna.UI2.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "System.CodeDom/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/7.0.0": {"type": "package", "dependencies": {"System.CodeDom": "7.0.0"}, "compile": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"FontAwesome.Sharp/6.6.0": {"sha512": "bJCb2x73UC1nD9+9iMLaUy6Ujf9Pf+buL6FHwVnaoDYT4CQNLazz59hO5gQ0+SMSW0G+ZXmjjShOYr3Jn2UZiw==", "type": "package", "path": "fontawesome.sharp/6.6.0", "files": [".nupkg.metadata", ".signature.p7s", "fontawesome.sharp.6.6.0.nupkg.sha512", "fontawesome.sharp.nuspec", "lib/net472/FontAwesome.Sharp.dll", "lib/net48/FontAwesome.Sharp.dll", "lib/net6.0-windows7.0/FontAwesome.Sharp.dll", "lib/net7.0-windows7.0/FontAwesome.Sharp.dll", "lib/net8.0-windows7.0/FontAwesome.Sharp.dll", "lib/netcoreapp3.1/FontAwesome.Sharp.dll"]}, "Guna.UI2.WinForms/2.0.4.7": {"sha512": "8BdBYsfnntuICbFMHIZsd5morwURN8Ucsvq7wJeyLV1Z1ze6CAIxVKEirufgjGIiXfF0M0sCr9EVFloZSazWew==", "type": "package", "path": "guna.ui2.winforms/2.0.4.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "guna.ui2.winforms.2.0.4.7.nupkg.sha512", "guna.ui2.winforms.nuspec", "icon.png", "lib/net40/Guna.UI2.dll", "lib/net45/Guna.UI2.dll", "lib/net461/Guna.UI2.dll", "lib/net472/Guna.UI2.dll", "lib/net48/Guna.UI2.dll", "lib/net6.0-windows7.0/Guna.UI2.dll", "lib/net7.0-windows7.0/Guna.UI2.dll", "lib/netcoreapp3.1/Guna.UI2.dll"]}, "System.CodeDom/7.0.0": {"sha512": "GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "type": "package", "path": "system.codedom/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.7.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/7.0.0": {"sha512": "A4jed4QUviDOm7fJNKAJObEAEkEUXmkGL/w0iyCYTzrl1rezTj8LGFHfsVst4Vb9JwFcTpboiDrvdST48avBpw==", "type": "package", "path": "system.management/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "system.management.7.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["FontAwesome.Sharp >= 6.6.0", "Guna.UI2.WinForms >= 2.0.4.7"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\scan\\xx\\ModernWinApp\\ModernWinApp.csproj", "projectName": "ModernWinApp", "projectPath": "C:\\Users\\<USER>\\Desktop\\scan\\xx\\ModernWinApp\\ModernWinApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\scan\\xx\\ModernWinApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"FontAwesome.Sharp": {"target": "Package", "version": "[6.6.0, )"}, "Guna.UI2.WinForms": {"target": "Package", "version": "[2.0.4.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}