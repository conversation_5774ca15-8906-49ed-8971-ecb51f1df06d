using System.Drawing;

namespace ModernWinApp.Theme
{
    public static class ModernColorScheme
    {
        // Modern Color Palettes
        public enum ColorTheme
        {
            Ocean,      // Blue gradient theme
            Forest,     // Green gradient theme  
            Sunset,     // Orange/Purple gradient theme
            Midnight,   // Dark blue/purple theme
            Aurora,     // Multi-color gradient theme
            Corporate   // Professional blue/gray theme
        }

        public static ColorTheme CurrentTheme { get; set; } = ColorTheme.Ocean;

        // Ocean Theme (Default) - Modern Blue Gradients
        public static class Ocean
        {
            public static readonly Color Primary = ColorTranslator.FromHtml("#0F172A");           // Deep Navy
            public static readonly Color Secondary = ColorTranslator.FromHtml("#1E293B");        // Slate Gray
            public static readonly Color Accent = ColorTranslator.FromHtml("#3B82F6");           // Bright Blue
            public static readonly Color AccentLight = ColorTranslator.FromHtml("#60A5FA");      // Light Blue
            public static readonly Color AccentDark = ColorTranslator.FromHtml("#1D4ED8");       // Dark Blue
            public static readonly Color Surface = ColorTranslator.FromHtml("#334155");          // Medium Slate
            public static readonly Color Background = ColorTranslator.FromHtml("#0F172A");       // Deep Navy
            public static readonly Color Card = ColorTranslator.FromHtml("#1E293B");             // Card Background
            public static readonly Color Sidebar = ColorTranslator.FromHtml("#0C1220");          // Darker Sidebar
            public static readonly Color Success = ColorTranslator.FromHtml("#10B981");          // Emerald
            public static readonly Color Warning = ColorTranslator.FromHtml("#F59E0B");          // Amber
            public static readonly Color Error = ColorTranslator.FromHtml("#EF4444");            // Red
            public static readonly Color Info = ColorTranslator.FromHtml("#06B6D4");             // Cyan
        }

        // Forest Theme - Modern Green Gradients
        public static class Forest
        {
            public static readonly Color Primary = ColorTranslator.FromHtml("#064E3B");          // Dark Green
            public static readonly Color Secondary = ColorTranslator.FromHtml("#065F46");        // Forest Green
            public static readonly Color Accent = ColorTranslator.FromHtml("#10B981");           // Emerald
            public static readonly Color AccentLight = ColorTranslator.FromHtml("#34D399");      // Light Emerald
            public static readonly Color AccentDark = ColorTranslator.FromHtml("#059669");       // Dark Emerald
            public static readonly Color Surface = ColorTranslator.FromHtml("#047857");          // Green Surface
            public static readonly Color Background = ColorTranslator.FromHtml("#064E3B");       // Dark Green BG
            public static readonly Color Card = ColorTranslator.FromHtml("#065F46");             // Card Background
            public static readonly Color Sidebar = ColorTranslator.FromHtml("#022C22");          // Darker Sidebar
            public static readonly Color Success = ColorTranslator.FromHtml("#10B981");          // Emerald
            public static readonly Color Warning = ColorTranslator.FromHtml("#F59E0B");          // Amber
            public static readonly Color Error = ColorTranslator.FromHtml("#EF4444");            // Red
            public static readonly Color Info = ColorTranslator.FromHtml("#06B6D4");             // Cyan
        }

        // Sunset Theme - Orange/Purple Gradients
        public static class Sunset
        {
            public static readonly Color Primary = ColorTranslator.FromHtml("#451A03");          // Dark Brown
            public static readonly Color Secondary = ColorTranslator.FromHtml("#7C2D12");        // Dark Orange
            public static readonly Color Accent = ColorTranslator.FromHtml("#F97316");           // Orange
            public static readonly Color AccentLight = ColorTranslator.FromHtml("#FB923C");      // Light Orange
            public static readonly Color AccentDark = ColorTranslator.FromHtml("#EA580C");       // Dark Orange
            public static readonly Color Surface = ColorTranslator.FromHtml("#9A3412");          // Orange Surface
            public static readonly Color Background = ColorTranslator.FromHtml("#451A03");       // Dark Brown BG
            public static readonly Color Card = ColorTranslator.FromHtml("#7C2D12");             // Card Background
            public static readonly Color Sidebar = ColorTranslator.FromHtml("#2C0A02");          // Darker Sidebar
            public static readonly Color Success = ColorTranslator.FromHtml("#10B981");          // Emerald
            public static readonly Color Warning = ColorTranslator.FromHtml("#F59E0B");          // Amber
            public static readonly Color Error = ColorTranslator.FromHtml("#EF4444");            // Red
            public static readonly Color Info = ColorTranslator.FromHtml("#8B5CF6");             // Purple
        }

        // Midnight Theme - Dark Blue/Purple
        public static class Midnight
        {
            public static readonly Color Primary = ColorTranslator.FromHtml("#1E1B4B");          // Dark Indigo
            public static readonly Color Secondary = ColorTranslator.FromHtml("#312E81");        // Indigo
            public static readonly Color Accent = ColorTranslator.FromHtml("#8B5CF6");           // Purple
            public static readonly Color AccentLight = ColorTranslator.FromHtml("#A78BFA");      // Light Purple
            public static readonly Color AccentDark = ColorTranslator.FromHtml("#7C3AED");       // Dark Purple
            public static readonly Color Surface = ColorTranslator.FromHtml("#3730A3");          // Purple Surface
            public static readonly Color Background = ColorTranslator.FromHtml("#1E1B4B");       // Dark Indigo BG
            public static readonly Color Card = ColorTranslator.FromHtml("#312E81");             // Card Background
            public static readonly Color Sidebar = ColorTranslator.FromHtml("#0F0C29");          // Darker Sidebar
            public static readonly Color Success = ColorTranslator.FromHtml("#10B981");          // Emerald
            public static readonly Color Warning = ColorTranslator.FromHtml("#F59E0B");          // Amber
            public static readonly Color Error = ColorTranslator.FromHtml("#EF4444");            // Red
            public static readonly Color Info = ColorTranslator.FromHtml("#06B6D4");             // Cyan
        }

        // Aurora Theme - Multi-color Gradient
        public static class Aurora
        {
            public static readonly Color Primary = ColorTranslator.FromHtml("#0C0A1E");          // Very Dark Purple
            public static readonly Color Secondary = ColorTranslator.FromHtml("#1A1B3A");        // Dark Purple
            public static readonly Color Accent = ColorTranslator.FromHtml("#6366F1");           // Indigo
            public static readonly Color AccentLight = ColorTranslator.FromHtml("#818CF8");      // Light Indigo
            public static readonly Color AccentDark = ColorTranslator.FromHtml("#4F46E5");       // Dark Indigo
            public static readonly Color Surface = ColorTranslator.FromHtml("#2D2A5A");          // Purple Surface
            public static readonly Color Background = ColorTranslator.FromHtml("#0C0A1E");       // Very Dark BG
            public static readonly Color Card = ColorTranslator.FromHtml("#1A1B3A");             // Card Background
            public static readonly Color Sidebar = ColorTranslator.FromHtml("#050314");          // Darker Sidebar
            public static readonly Color Success = ColorTranslator.FromHtml("#10B981");          // Emerald
            public static readonly Color Warning = ColorTranslator.FromHtml("#F59E0B");          // Amber
            public static readonly Color Error = ColorTranslator.FromHtml("#EF4444");            // Red
            public static readonly Color Info = ColorTranslator.FromHtml("#06B6D4");             // Cyan
        }

        // Corporate Theme - Professional Blue/Gray
        public static class Corporate
        {
            public static readonly Color Primary = ColorTranslator.FromHtml("#1F2937");          // Gray 800
            public static readonly Color Secondary = ColorTranslator.FromHtml("#374151");        // Gray 700
            public static readonly Color Accent = ColorTranslator.FromHtml("#2563EB");           // Blue 600
            public static readonly Color AccentLight = ColorTranslator.FromHtml("#3B82F6");      // Blue 500
            public static readonly Color AccentDark = ColorTranslator.FromHtml("#1D4ED8");       // Blue 700
            public static readonly Color Surface = ColorTranslator.FromHtml("#4B5563");          // Gray 600
            public static readonly Color Background = ColorTranslator.FromHtml("#1F2937");       // Gray 800 BG
            public static readonly Color Card = ColorTranslator.FromHtml("#374151");             // Card Background
            public static readonly Color Sidebar = ColorTranslator.FromHtml("#111827");          // Gray 900
            public static readonly Color Success = ColorTranslator.FromHtml("#10B981");          // Emerald
            public static readonly Color Warning = ColorTranslator.FromHtml("#F59E0B");          // Amber
            public static readonly Color Error = ColorTranslator.FromHtml("#EF4444");            // Red
            public static readonly Color Info = ColorTranslator.FromHtml("#06B6D4");             // Cyan
        }

        // Text Colors (Universal)
        public static readonly Color TextPrimary = Color.FromArgb(255, 255, 255);               // Pure White
        public static readonly Color TextSecondary = Color.FromArgb(203, 213, 225);             // Light Gray
        public static readonly Color TextMuted = Color.FromArgb(148, 163, 184);                 // Muted Gray
        public static readonly Color TextDisabled = Color.FromArgb(100, 116, 139);              // Disabled Gray

        // Gradient Colors
        public static class Gradients
        {
            public static readonly Color[] OceanGradient = { 
                ColorTranslator.FromHtml("#0F172A"), 
                ColorTranslator.FromHtml("#1E293B"), 
                ColorTranslator.FromHtml("#334155") 
            };
            
            public static readonly Color[] AccentGradient = { 
                ColorTranslator.FromHtml("#3B82F6"), 
                ColorTranslator.FromHtml("#1D4ED8"), 
                ColorTranslator.FromHtml("#1E40AF") 
            };
            
            public static readonly Color[] SuccessGradient = { 
                ColorTranslator.FromHtml("#10B981"), 
                ColorTranslator.FromHtml("#059669"), 
                ColorTranslator.FromHtml("#047857") 
            };
        }

        // Get Current Theme Colors
        public static Color GetPrimary() => CurrentTheme switch
        {
            ColorTheme.Ocean => Ocean.Primary,
            ColorTheme.Forest => Forest.Primary,
            ColorTheme.Sunset => Sunset.Primary,
            ColorTheme.Midnight => Midnight.Primary,
            ColorTheme.Aurora => Aurora.Primary,
            ColorTheme.Corporate => Corporate.Primary,
            _ => Ocean.Primary
        };

        public static Color GetSecondary() => CurrentTheme switch
        {
            ColorTheme.Ocean => Ocean.Secondary,
            ColorTheme.Forest => Forest.Secondary,
            ColorTheme.Sunset => Sunset.Secondary,
            ColorTheme.Midnight => Midnight.Secondary,
            ColorTheme.Aurora => Aurora.Secondary,
            ColorTheme.Corporate => Corporate.Secondary,
            _ => Ocean.Secondary
        };

        public static Color GetAccent() => CurrentTheme switch
        {
            ColorTheme.Ocean => Ocean.Accent,
            ColorTheme.Forest => Forest.Accent,
            ColorTheme.Sunset => Sunset.Accent,
            ColorTheme.Midnight => Midnight.Accent,
            ColorTheme.Aurora => Aurora.Accent,
            ColorTheme.Corporate => Corporate.Accent,
            _ => Ocean.Accent
        };

        public static Color GetAccentLight() => CurrentTheme switch
        {
            ColorTheme.Ocean => Ocean.AccentLight,
            ColorTheme.Forest => Forest.AccentLight,
            ColorTheme.Sunset => Sunset.AccentLight,
            ColorTheme.Midnight => Midnight.AccentLight,
            ColorTheme.Aurora => Aurora.AccentLight,
            ColorTheme.Corporate => Corporate.AccentLight,
            _ => Ocean.AccentLight
        };

        public static Color GetAccentDark() => CurrentTheme switch
        {
            ColorTheme.Ocean => Ocean.AccentDark,
            ColorTheme.Forest => Forest.AccentDark,
            ColorTheme.Sunset => Sunset.AccentDark,
            ColorTheme.Midnight => Midnight.AccentDark,
            ColorTheme.Aurora => Aurora.AccentDark,
            ColorTheme.Corporate => Corporate.AccentDark,
            _ => Ocean.AccentDark
        };

        public static Color GetSurface() => CurrentTheme switch
        {
            ColorTheme.Ocean => Ocean.Surface,
            ColorTheme.Forest => Forest.Surface,
            ColorTheme.Sunset => Sunset.Surface,
            ColorTheme.Midnight => Midnight.Surface,
            ColorTheme.Aurora => Aurora.Surface,
            ColorTheme.Corporate => Corporate.Surface,
            _ => Ocean.Surface
        };

        public static Color GetBackground() => CurrentTheme switch
        {
            ColorTheme.Ocean => Ocean.Background,
            ColorTheme.Forest => Forest.Background,
            ColorTheme.Sunset => Sunset.Background,
            ColorTheme.Midnight => Midnight.Background,
            ColorTheme.Aurora => Aurora.Background,
            ColorTheme.Corporate => Corporate.Background,
            _ => Ocean.Background
        };

        public static Color GetCard() => CurrentTheme switch
        {
            ColorTheme.Ocean => Ocean.Card,
            ColorTheme.Forest => Forest.Card,
            ColorTheme.Sunset => Sunset.Card,
            ColorTheme.Midnight => Midnight.Card,
            ColorTheme.Aurora => Aurora.Card,
            ColorTheme.Corporate => Corporate.Card,
            _ => Ocean.Card
        };

        public static Color GetSidebar() => CurrentTheme switch
        {
            ColorTheme.Ocean => Ocean.Sidebar,
            ColorTheme.Forest => Forest.Sidebar,
            ColorTheme.Sunset => Sunset.Sidebar,
            ColorTheme.Midnight => Midnight.Sidebar,
            ColorTheme.Aurora => Aurora.Sidebar,
            ColorTheme.Corporate => Corporate.Sidebar,
            _ => Ocean.Sidebar
        };

        // Status Colors (Universal across themes)
        public static Color GetSuccess() => ColorTranslator.FromHtml("#10B981");
        public static Color GetWarning() => ColorTranslator.FromHtml("#F59E0B");
        public static Color GetError() => ColorTranslator.FromHtml("#EF4444");
        public static Color GetInfo() => ColorTranslator.FromHtml("#06B6D4");

        // Theme Change Event
        public static event EventHandler? ThemeChanged;

        public static void ChangeTheme(ColorTheme newTheme)
        {
            CurrentTheme = newTheme;
            ThemeChanged?.Invoke(null, EventArgs.Empty);
        }

        // Get Theme Display Names in Arabic
        public static string GetThemeDisplayName(ColorTheme theme) => theme switch
        {
            ColorTheme.Ocean => "المحيط الأزرق",
            ColorTheme.Forest => "الغابة الخضراء", 
            ColorTheme.Sunset => "غروب الشمس",
            ColorTheme.Midnight => "منتصف الليل",
            ColorTheme.Aurora => "الشفق القطبي",
            ColorTheme.Corporate => "المؤسسي الاحترافي",
            _ => "المحيط الأزرق"
        };
    }
}
